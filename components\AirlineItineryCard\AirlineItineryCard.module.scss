@import '../../styles/variable.scss';

.itinerary-flight-card-div{
    margin-bottom: 10px;
    .flex_row{
        display: flex;
        flex-direction: row;
    }
    .align_between_stretch {
        height: 100%;
        box-sizing: border-box;
        place-content: stretch space-between;
        align-items: stretch;
        max-height: 100%;
    }
    .align_between_center {
        height: 100%;
        box-sizing: border-box;
        place-content: center space-between;
        align-items: center;
    }
    .flex_column {
        display: flex;
        flex-direction: column;
    }
    .align_center_stretch {
        height: 100%;
        box-sizing: border-box;
        place-content: stretch center;
        align-items: stretch;
        max-height: 100%;
    }
    .align_center_end {
        height: 100%;
        box-sizing: border-box;
        place-content: flex-end center;
        align-items: flex-end;
    }
    .alihn_start_center {
        height: 100%;
        box-sizing: border-box;
        place-content: center flex-start;
        align-items: center;
    }
    .align_center_start {
        height: 100%;
        box-sizing: border-box;
        place-content: flex-start center;
        align-items: flex-start;
    }
    .align_end_center {
        height: 100%;
        box-sizing: border-box;
        place-content: center flex-end;
        align-items: center;
    }
    .align_between_end {
        height: 100%;
        box-sizing: border-box;
        place-content: flex-end space-between;
        align-items: flex-end;
    }
    .align_center_center {
        height: 100%;
        box-sizing: border-box;
        place-content: center;
        align-items: center;
    }
    .shine {
        background: #f6f7f8;
        animation: shimmer 1.5s infinite linear;
        background: linear-gradient(
            to right,
            #e0e0e0 0%,
            #f7f7f7 50%,
            #e0e0e0 100%
        );
        background-size: 200% 100%;
    }
    
    @keyframes shimmer {
        0% {
            background-position: -100% 0;
        }
        100% {
            background-position: 100% 0;
        }
    }
    .top-head-div{
        width: 100%;
        gap: 4px;
        .head-left{
            background: #fff;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            padding: 7px 15px;
            width: 100%;
            .left{
                width: 50%;
                gap: 10px;
                .image-main-mac{
                    height: 30px;
                    width: 30px;
                    margin-top: auto;
                    margin-bottom: auto;
                    img{
                        height: 100%;
                        width: 100%;
                    }
                }
                .depart-txt{
                    font-size: 14px;
                    color: #000;
                    font-weight: 500;
                }
                .date-txt{
                    font-size: 15px;
                    color: #000;
                    font-weight: 500;
                }
                .from-to{
                    font-size: 18px;
                    color: #000;
                    display: flex;
                    gap: 5px;
                    align-items: center;
                    font-weight: 500;
                    .icon-rotate{
                        transform: rotate(90deg);
                    }
                }
                .distance-time{
                    font-size: 13px;
                    color: #686868;
                }
            }
            .line-divide{
                border-right: 1px solid #d0d0d0;
                height: 35px;
            }
            .right{
                width: 50%;
                gap: 10px;
                .gray-text{
                    font-size: 12px;
                    color:#595959;
                    font-weight: 500;
                }
                .pnr-copy {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    font-size: 13px;
                    color: #8b8b8b;
                    .pnr-txt{
                        color: #000;
                        font-weight: 700;
                    }
                    .icons{
                        cursor: pointer;
                    }
                }
                .refundable {
                    color: #0fb332;
                }
                .non_refundable {
                    color: #f00;
                }
            }
        }
        .head-right{
            background: #fff;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            padding: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            .dwn-icon{
                transition: all .25s linear;
                transform: rotate(180deg);
                font-size: 20px;
                color: #696969;
            }
            .rotate-dwn-icon{
                transform: rotate(0deg);
            }
        }
    }
    .flight-travel-details-div{
        margin-top: 5px;
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        width: 100%;
        .flight-review-div{
            width: 100%;
            padding: 10px 15px;
            .airline-details-div{
                .flight-name{
                    width: 100%;
                    border-bottom: 1px solid #d0d0d0;
                    padding-bottom: 3px;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    .flight-logo{
                        height: 30px;
                        width: 30px;
                        img{
                            height: 100%;
                            width: 100%;
                        }
                    }
                    .name-number-div{
                        .name-txt{
                            font-size: 14px;
                            color: #000;
                            font-weight: 500;
                        }
                        .number-txt{
                            font-size: 12px;
                            color: #a2a1a1;
                            text-transform: uppercase;
                        }
                    }
                }
                .flight-type-gp{
                    font-size: 12px;
                    color: #9d9d9d;
                    padding: 3px 0;
                    border-bottom: 1px solid #d0d0d0;
                    p{
                        line-height: 12px;
                        margin-bottom: 3px;
                    }
                }
            }
            .from-to-time-div{
                height: auto;
                .box{
                    text-align: end;
                    h2{
                        font-size: 20px;
                        font-weight: 700;
                        color: #000;
                        line-height: 20px;
                        margin-bottom: 0;
                    }
                    p{
                        color: #9d9d9d;
                        font-size: 14px;
                        white-space: nowrap;
                        margin-bottom: 0;
                    }
                    .next-day{
                        font-size: 12px;
                        line-height: 12px;
                    }
                }
                .left{
                    gap: 15px;
                }
                .center{
                    width: 50px;
                    padding: 16px 0 35px;
                    .divider{
                        position: relative;
                        border-left: 3px dotted #a9a8a8;
                        background-color: #fff;
                        &::after{
                            content: "";
                            border: 3px solid $primary_color;
                            border-radius: 12px;
                            box-sizing: border-box;
                            height: 14px;
                            width: 14px;
                            position: absolute;
                            bottom: -12px;
                            left: -8px;
                        }
                        &::before{
                            content: "";
                            border: 3px solid $primary_color;
                            border-radius: 12px;
                            box-sizing: border-box;
                            height: 14px;
                            width: 14px;
                            position: absolute;
                            top: -12px;
                            left: -8px;
                        }
                    }
                }
                .sector{
                    h2{
                        font-size: 16px;
                        line-height: 16px;
                        font-weight: 700;
                        color: #000;
                        margin-bottom: 0;
                    }
                    p{
                        font-size: 12px;
                        font-weight: 400;
                        color: #9d9d9d;
                        margin-bottom: 0;
                    }
                }
                .time-div{
                    h2{
                        font-size: 15px;
                        line-height: 15px;
                    }
                }
                .right{
                    gap: 15px;
                    
                }
            }
            .baggage-confirmation{
                height: auto;
                gap: 6px;
                .status{
                    font-size: 13px;
                    color: #fff;
                    font-weight: 400;
                    padding: 0 5px;
                    border-radius: 4px;
                    height: 18px;
                    line-height: 18px;
                    margin-bottom: 10px;
                    &.confirmed {
                        color: #479d35;
                    }
                    &.statusfailed {
                        color: #f00;
                    }
                    &.statuscancelled { 
                        color: #878585;
                    }
                    &.statuspartcancelled {
                        color: #006ca0;
                    }
                    &.statusrefunded {
                        color: #8d0030;
                    }
                    &.statusinprogress {
                        color: #009a9c;
                    }
                    &.statuscanrequest {
                        color: #fedd00;
                    }
                    &.statuscancldreqst {
                        color: #b20082;
                    }
                    &.statuscanaprvd {
                        color: #656565;
                    }
                    &.statusrejected {
                        color: #007cff;
                    }
                    &.status-verified {
                        color: #5bae01;
                    }
                    &.statusreissuereq {
                        color: #d1dc80;;
                    }
                    &.statusreissuerej {
                          color: #007cff;
                    }
                    &.statusonhold{
                        color: #ff7800;
                    }
                    &.statusonholdfailed{
                        color: #ee6a6a;
                    }
                }
                .baggagebox{
                    background: #f6f6f6;
                    border-radius: 4px;
                    padding: 10px;
                    width: 100%;
                    .baggageboxtop{
                        width: 100%;
                        border-bottom: 1px solid #d0d0d0;
                        padding: 5px 0;
                        gap: 5px;
                        img{
                            max-width: 100%;
                            height: auto;
                            display: inline-block;
                        }
                        .right{
                            font-size: 12px;
                            color: #000;
                            text-transform: uppercase;
                            font-weight: 600;
                            p{
                                line-height: 12px;
                                margin-bottom: 0;
                            }
                        }
                    }
                    .baggageboxbottom{
                        width: 100%;
                        padding-top: 10px;
                        gap: 10px;
                        .baggage-loader{
                            width: 140px;
                            height: 50px;
                        }
                        .label{
                            font-size: 12px;
                            color: #777;
                            text-transform: uppercase;
                        }
                        .values{
                            font-size: 12px;
                            color: #000;
                            white-space: nowrap;
                        }
                        .divider{
                            border-right: 1px solid #d0d0d0;
                        }
                    }
                }
            }
        }
        .change-flight-div{
            padding: 10px 15px;
            background-color: #c3996b3c;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
            .icons{
                font-size: 25px;
            }
        }
        .notice-div{
            padding: 10px 15px;
            border-top: 1px solid #d0d0d0;
            height: 100%;
            flex-direction: column;
            box-sizing: border-box;
            display: flex;
            place-content: stretch flex-start;
            align-items: stretch;
            max-width: 100%;
            gap: 5px;
            .icon-notice-txt{
                display: flex;
                gap: 10px;
                .icon{
                    color: $text_color;
                    font-size: 14px;
                }
                .notice-text{
                    font-size: 12px;
                }
            }
        }
    }
    
}

.fare-rule-pdng-div{
    padding: 20px;
}


@media only screen and (max-width: 768px) {
    .itinerary-flight-card-div{
        .top-head-div{
            background-color: #fff;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            .head-left{
                background: none;
                border: none;
                border-radius: 0;
                display: block;
                padding: 7px 10px;
                .left{
                    width: 100%;
                    .depart-date-div{
                        place-content: flex-start center;
                        align-items: flex-start;
                        margin-bottom: 0;
                        background-color: #525252;
                        border-radius: 4px;
                        gap: 2px;
                        padding: 5px;
                    }
                    .depart-txt{
                        font-size: 11px;
                        line-height: 11px;
                        color: #fff;
                    }
                    .date-txt{
                        font-size: 12px;
                        line-height: 12px;
                        color: #fff;
                    }
                    .from-to{
                        font-size: 14px;
                    }
                }
                .line-divide{
                    display: none;
                }
                .right{
                    width: 100%;
                    gap: 5px;
                    margin-top: 10px;
                    place-content: center space-between;
                    flex-wrap: wrap;
                    .pnr-copy {
                        font-size: 12px;
                    }
                }
            }
            .head-right{
                border: none;
                padding: 10px;
            }
        }
        .flight-travel-details-div{
            .flight-review-div{
                flex-direction: column;
                gap: 15px;
                padding: 10px;
                .airline-details-div{
                    display: flex;
                    justify-content: space-between;
                    border-bottom: 1px solid #d0d0d0;
                    .flight-name{
                        width: auto;
                        border: none;
                    }
                    .flight-type-gp{
                        border: none;
                    }
                }
                .from-to-time-div{
                    padding-bottom: 10px;
                    border-bottom: 1px solid #d0d0d0;
                    .left{
                        height: auto;
                        .box{
                            h2{
                                font-size: 14px;
                            }
                            p{
                                font-size: 12px;
                            }
                        }
                    }
                    .center{
                        height: auto;
                    }
                    .right{
                        height: auto;
                        .sector{
                            h2{
                                font-size: 13px;
                            }
                            p{
                                line-height: 12px;
                            }
                        }
                        .time-div{
                            h2{
                                font-size: 12px;
                                line-height: 12px;
                            }
                        }
                    }
                }
                .baggage-confirmation{
                    place-content: center space-between;
                    align-items: center;
                    .status-book{
                        font-size: 12px;
                    }
                }
            }
            .change-flight-div{
                font-size: 10px;
                padding: 10px;
            }
        }
    }
    .fare-rule-pdng-div{
        padding: 10px;
    }
}