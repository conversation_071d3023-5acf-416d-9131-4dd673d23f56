"use client";

import { FareSummaryDetails } from 'models/flight-info-ssr-model';
import styles from './FareSummary.module.scss';
import { useState } from 'react';
import { KeyboardArrowDown } from '@mui/icons-material';
interface FareSummaryProp{
    fareSummary: FareSummaryDetails | undefined;
    currency: string ;
}

export function FareSummary({currency='INR',fareSummary}:FareSummaryProp) {

    function FlightCurrencyConverter(arg0: number): import("react").ReactNode {
        return arg0
    }
    const [isBaseFareOpen,setIsBaseFareOpen] = useState<boolean>(false)
    const [isTaxOpen,setIsTaxOpen] = useState<boolean>(false)
    const [isAddonOpen,setIsAddonOpen] = useState<boolean>(false)

    return (
        <>

            <div className={styles["flight-review-fare-summary-div"]}>
                <div className={styles["head-summary-div"]}>Fare Details</div>
                <div className={styles["border-fare-summry"]}>
                    {/* Base Fare Section */}
                    <div className={styles["expansion-panel"]}>
                        <div className={styles["expansion-panel-header"]}>
                            <div onClick={() => setIsBaseFareOpen(!isBaseFareOpen)} className={styles["panel-title"]}><KeyboardArrowDown className={`${styles["arrow-icon"]} ${isBaseFareOpen ? styles["rotate"] : ""}`}/>  Base Fare</div>
                            <h5>{FlightCurrencyConverter(fareSummary?.baseFare?.total || 0)}</h5>
                        </div>
                        {fareSummary?.baseFare?.subFare?.filter((k) => k.title && k.totalPrice > 0).map((k, index) => (
                            <div className={`${styles["datas"]} ${isBaseFareOpen  ? styles["open"] : styles["closed"]}`} key={index}>
                                {k.totalPrice > 0 && (
                                    <>
                                        <p>
                                            {k?.title} ({k?.count} X {FlightCurrencyConverter(k?.price)})
                                        </p>
                                        <h6>{FlightCurrencyConverter(k?.totalPrice)}</h6>
                                    </>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Tax & Charges Section */}
                    <div className={styles["expansion-panel"]}>
                        <div className={styles["expansion-panel-header"]}>
                            <div onClick={() => setIsTaxOpen(!isTaxOpen)} className={styles["panel-title"]}><KeyboardArrowDown className={`${styles["arrow-icon"]} ${isTaxOpen ? styles["rotate"] : ""}`}/> Tax & Charges</div>
                            <h5>{FlightCurrencyConverter(fareSummary?.taxs?.total || 0)}</h5>
                        </div>
                        {fareSummary?.taxs?.subFare?.filter((k) => k?.title && k?.totalPrice > 0).map((k, index) => (
                            <div className={`${styles["datas"]} ${isTaxOpen  ? styles["open"] : styles["closed"]}`} key={index}>
                                {k.totalPrice > 0 && (
                                    <>
                                        <p>{k?.title}</p>
                                        <h6>{FlightCurrencyConverter(k?.totalPrice)}</h6>
                                    </>
                                )}
                            </div>
                        ))}
                    </div>

                    {/* Addons Section */}
                    {fareSummary?.addons?.total ? 
                        fareSummary?.addons?.total > 0 && (
                        <div className={styles["expansion-panel"]}>
                            <div className={styles["expansion-panel-header"]}>
                                <div onClick={() => setIsAddonOpen(!isAddonOpen)} className={styles["panel-title"]}><KeyboardArrowDown className={`${styles["arrow-icon"]} ${isAddonOpen ? styles["rotate"] : ""}`}/> Addons</div>
                                <h5>{FlightCurrencyConverter(fareSummary?.addons?.total)}</h5>
                            </div>
                            {fareSummary?.addons?.subFare?.filter((k)=> k?.title && k?.totalPrice > 0).map((k, index) => (
                                <div className={`${styles["datas"]} ${isAddonOpen  ? styles["open"] : styles["closed"]}`} key={index}>
                                    {k?.totalPrice > 0 && (
                                        <>
                                            <p>{k?.title}</p>
                                            <h6>{FlightCurrencyConverter(k?.totalPrice)}</h6>
                                        </>
                                    )}
                                </div>
                            ))}
                        </div>
                    ):(
                        <></>
                    )}

                    {/* Total Amount */}
                    <div className={styles["total-div"]}>
                        <h3>Total Amount:</h3>
                        <h3>{FlightCurrencyConverter(fareSummary?.totalAmount || 0)}</h3>
                    </div>
                </div>
            </div>

        </>
    );
}