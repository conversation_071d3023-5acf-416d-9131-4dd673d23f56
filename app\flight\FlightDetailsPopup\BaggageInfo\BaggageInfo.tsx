"use client";

import { FlightBaggageInfo } from 'models/flight-info-ssr-model';
import styles from './BaggageInfo.module.scss';
import { useState } from 'react';

interface BaggageInfoProps{
    flightBaggage: FlightBaggageInfo[];
}

export function FlightBaggageInfoComponent({flightBaggage = []}:BaggageInfoProps) {

    return (
        <>

            <div className={styles["flight-details-baggage-info"]}>
                <div className={styles["table-brdr-div"]}>
                    <div className={`${styles["column-tbl-div"]} ${styles['table-head-clmn']}`}>
                        <div className={styles["sector-div"]}>
                            <h3>Sector/Flight</h3>
                        </div>
                        <div className={styles["bagage-div"]}>
                            <h3>Check in Baggage</h3>
                        </div>
                        <div className={styles["bagage-div"]}>
                            <h3>Cabin Baggage</h3>
                        </div>
                    </div>

                    {flightBaggage.map((bg, index) => (
                        <div key={index} className={styles["column-tbl-div"]}>
                            <div className={styles["sector-div"]}>
                                <p>{`${bg.from} - ${bg.to}`}</p>
                            </div>
                            <div className={styles["bagage-div"]}>
                                <p>
                                    {bg.checkin.adult && <span>{bg.checkin.adult} (Adult)</span>}
                                    {bg.checkin.child && <span>, {bg.checkin.child} (Child)</span>}
                                    {bg.checkin.infant && <span>, {bg.checkin.infant} (Infant)</span>}
                                </p>
                            </div>
                            <div className={styles["bagage-div"]}>
                                <p>
                                    {bg.cabin.adult && <span>{bg.cabin.adult} (Adult)</span>}
                                    {bg.cabin.child && <span>, {bg.cabin.child} (Child)</span>}
                                    {bg.cabin.infant && <span>, {bg.cabin.infant} (Infant)</span>}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>

                <div className={styles["text-rules-div"]}>
                    <div className={styles["text-icon"]}>
                        <span className={styles["fa fa-info-circle icon-clr"]}></span>
                        <span>
                            The information presented above is as obtained from the airline reservation system. We
                            do not guarantee the accuracy of this information.
                        </span>
                    </div>
                    <div className={styles["text-icon"]}>
                        <span className={styles["fa fa-info-circle icon-clr"]}></span>
                        <span>
                            The baggage allowance may vary according to stop-overs, connecting flights, and changes in airline
                            rules.
                        </span>
                    </div>
                </div>

                <div className={styles["dainger-info-div"]}>
                    <span className={styles["fa fa-info-circle"]}></span>
                    <span>
                        Adding of additional baggage is subject to load factor of the flight. In case if baggage could not be
                        added, payment for the additional baggage paid will be reverted.
                    </span>
                </div>
            </div>

        </>
    );
}