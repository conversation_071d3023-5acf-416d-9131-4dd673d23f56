import React, { useEffect } from 'react';
import styles from './BottomUpPopup.module.scss';

interface  Props {
    children: React.ReactNode;
    isOpen: boolean;
    heading: string;
    zindex?: number;
    OnClose: () => void ;
}

const  BottomUpPopup:React.FC<Props> = ({isOpen = false,OnClose,children,zindex = 201,heading=''}) => {
    function close(){
        OnClose();
    }
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }
        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [isOpen]);
    return (
        <div  className={`${styles['bottom-popup-container']} ${isOpen ? styles.visible : ''}`}>
            <div onClick={close} style={{ zIndex: zindex - 10 }} className={`${styles['dy-bootom-up-popup-overlay']}`}></div>
            <div style={{ zIndex: zindex }} className={`${styles['dy-bootom-up-popup-div']} ${isOpen ? styles.show : styles.hide}`}>
                <div className={`${styles['header-menu-div']}`}>
                    <div className={styles["head-text"]}>{ heading }</div>
                    <span className={styles["fa fa-times icons"]} onClick={close}></span>
                </div>
                <div className={styles["body-div"]}>
                    {children}
                </div>
            </div>
        </div>
    );
};
export default  BottomUpPopup;