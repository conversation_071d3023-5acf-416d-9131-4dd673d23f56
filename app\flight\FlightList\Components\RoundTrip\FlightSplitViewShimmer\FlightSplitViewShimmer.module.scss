.shine {
    background: #f6f7f8;
    animation: shimmer 1.5s infinite linear;
    background: linear-gradient(
        to right,
        #e0e0e0 0%,
        #f7f7f7 50%,
        #e0e0e0 100%
    );
    background-size: 200% 100%;
}

@keyframes shimmer {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}


.shimmer_split_view_card{
    transition: all .1s ease-in;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #ffff;
    height: 100%;
    flex-direction: column;
    display: flex;
    place-content: stretch flex-start;
    align-items: stretch;
    max-width: 100%;
    gap: 10px;
    box-sizing: border-box;
    cursor: pointer;
    .top_div{
        display: flex;
        justify-content: space-between;
        .left_div{
            height: 16px;
            width: 50%;
        }
        .right_div{
            height: 16px;
            width: 20%;
        }
    }
    .flight_details{
        width: 100%;
        display: flex;
        justify-content: flex-end;
        .link_div{
            width: 30%;
            height: 20px;
        }
    }
    .center_div{
        display: flex;
        align-items: center;
        gap: 10px;
        .img_div{
            height: 32px;
            width: 32px;
        }
        .other_div{
            width: calc(100% - 42px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            .dep_div{
                width: 22%;
                .time_plce{
                    width: 80%;
                    height: 35px;
                }
            }
            .ret_div{
                display: flex;
                justify-content: flex-end;
            }
            .duration_div{
                width: 31%;
                display: flex;
                flex-direction: column;
                place-content: center flex-start;
                align-items: center;

                .connections_text {
                    height: 14px;
                    width: 80%;
                }
                .stop_div{
                    height: 14px;
                    width: 50%;
                }
                .flight_line_div {
                    width: 100%;
                    position: relative;
                    height: 8px;

                    &::before {
                        content: "";
                        left: 0;
                        width: calc(100% - 10px);
                        height: 0;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        border-bottom: 1px dashed #7d7b89;
                        z-index: 0;
                    }

                    &::after {
                        content: "\e90a";
                        transform: rotate(90deg);
                        position: absolute;
                        right: 3px;
                        top: -6.5px;
                        font-family: icomoon !important;
                        font-size: 11px;
                        color: #c1c1c1;
                    }
                }
            }
        }
    }
}