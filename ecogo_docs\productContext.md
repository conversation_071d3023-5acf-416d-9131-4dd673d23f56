# DigiYatra Flight Booking Platform

## Project Overview
DigiYatra is a comprehensive flight booking platform that allows users to search, compare, and book flights. The platform provides a seamless user experience from registration to booking completion.

## Core Problems Solved
- Simplified flight search and booking process
- User registration and authentication
- Flight comparison with multiple fare types
- Comprehensive booking management
- Travel document and service management

## Key Features
- User registration with OTP verification
- Airport search and selection
- Flight search with multiple criteria
- Flight details and fare comparison
- Booking management and traveler information
- Add-on services (SSR, seat selection, baggage)
- Payment processing and booking confirmation

## Target Users
- Individual travelers looking for flight bookings
- Business travelers requiring flexible booking options
- Travel agents managing multiple bookings

## Technology Stack
- Frontend: Next.js with TypeScript
- UI Framework: React with Material-UI components
- Styling: SCSS for custom styling
- API Communication: Axios for HTTP requests
- State Management: React hooks and local storage
