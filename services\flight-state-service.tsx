import { scheduleSave } from 'models/flight-list-response-model';
import React, { createContext, useContext, useState, ReactNode } from 'react';


// Define the context type
interface FlightStateContextType {
  isFlightDetailsPopup: boolean;
  setIsFlightDetailsPopup: React.Dispatch<React.SetStateAction<boolean>>;
  flightDetailsBodyData: any;
  setFlightDetailsBodyData: React.Dispatch<React.SetStateAction<any>>;
  flightSchedule: scheduleSave[];
  setFlightSchedule: React.Dispatch<React.SetStateAction<scheduleSave[]>>;
}

// Create the context
const FlightStateContext = createContext<FlightStateContextType | undefined>(undefined);

// Custom hook to use FlightStateContext
export const useFlightState = (): FlightStateContextType => {
  const context = useContext(FlightStateContext);
  if (!context) {
    throw new Error('useFlightState must be used within a FlightStateProvider');
  }
  return context;
};

// Define the Props for the Provider
interface FlightStateProviderProps {
  children: ReactNode;
}

// FlightStateProvider component
export const FlightStateProvider: React.FC<FlightStateProviderProps> = ({ children }) => {
  const [isFlightDetailsPopup, setIsFlightDetailsPopup] = useState<boolean>(false);
  const [flightDetailsBodyData, setFlightDetailsBodyData] = useState<any>(null);
  const [flightSchedule, setFlightSchedule] = useState<scheduleSave[]>([]);

  // Memoized context value
  const value = {
    isFlightDetailsPopup,
    setIsFlightDetailsPopup,
    flightDetailsBodyData,
    setFlightDetailsBodyData,
    flightSchedule,
    setFlightSchedule,
  };

  return (
    <FlightStateContext.Provider value={value}>
      {children}
    </FlightStateContext.Provider>
  );
};
