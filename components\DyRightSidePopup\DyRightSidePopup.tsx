import { CloseRounded } from '@mui/icons-material';
import { ReactNode } from 'react';
import styles from './DyRightSidePopup.module.scss'


interface RightSidePopupProps {
    children: ReactNode;
    // show: boolean;
    hide: VoidFunction;
    heading: string;
    zindex: number;
}

export function DyRightSidePopup({ children , heading = '', hide, zindex = 201 } : RightSidePopupProps){
    // [@OverlayAnimation]="show?'visible':'hidden'"
    return <>
    <div className={`${styles["dy-right-side-popup-overlay"]} `}  style={{ zIndex: zindex}}    onClick={hide}></div>
    {/* //[@rightSideBarAni]="show?'visible':'hidden'" */}
    <div className={styles["dy-right-side-popup-div"]}  style={{ zIndex: zindex}} >
        <div className={styles["header-menu-div"]}>
            <div className={styles["head-text"]}>{heading}</div>
            <span className={styles["icons"]} onClick={hide}><CloseRounded/></span>
        </div>
        <div className={styles["body-div"]}>
            {children}
        </div>
    </div>
    </>
    
}