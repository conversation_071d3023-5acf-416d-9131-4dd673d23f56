import { RestartAltRounded } from "@mui/icons-material";
import CloseIcon from '@mui/icons-material/Close'
import { useCallback, useEffect, useMemo, useState } from "react";
import { scheduleBodySet } from "adapters/flight-search-adapter";
import FilterFlight from "app/flight/FlightList/Components/Filter/FilterFlight";
import DyBottomTopPanel from "components/DyBottomTopPopup/DyBottomTopPopup";
import { FlightFilterHelper } from "helpers/flight-filter-helper";
import { FlightListHelper } from "helpers/flight-list-helper";
import { useSyncState } from "helpers/sync-state";
import { FlightDetailsEmitData } from "models/flight-info-ssr-model";
import { FlightSectorFilter, Journey, MCPaginateSplitTuis, MCSplitTuis, RTSelectTrip } from "models/flight-list-response-model";
import { ExpressSearchBody, FormSearch, GetExpressSearchBody } from "models/flight-search-model";
import FlightApiService from "services/flight-api-service";
import { useFlightState } from "services/flight-state-service";
import styles from './FlightRoundTripSplitView.module.scss'
import FlightSplitViewCard from "../FlightSplitViewCard/FlightSplitViewCard";
import FlightSplitViewShimmer from "../FlightSplitViewShimmer/FlightSplitViewShimmer";
interface FlightRoundTripSplitViewProps {
    currency:string
    isMobile: boolean
    getFlightSearchBody: FormSearch; // This replaces the Angular @Input()
}





export default function FlightRoundTripSplitView({  getFlightSearchBody, }: FlightRoundTripSplitViewProps) {
    const [listTrip, setListTrip, listTripRef] = useSyncState<MCSplitTuis[]>([]);
    const [TUI, setTUI] = useState<string>('')
    const [displayList, setDisplayList, DisplayListRef] = useSyncState<MCSplitTuis[]>([])
    const [PaginateList, setPaginateList,PaginateListRef] = useSyncState<MCPaginateSplitTuis[]>([])
    const [nextPageIndex, setNextPageIndex] = useState<number>(0)
    const [listShimmer, setListShimmer] = useState<boolean>(true)
    const [selectedTrip, setSelectedTrip,selectedTripRef] = useSyncState<RTSelectTrip[]>([])
    const [totalPrice, setTotalPrice] = useState<number>(0)
    const [filterData, setFilterData] = useState<FlightSectorFilter[]>([])
    const [filterShimmer, setFilterShimmer] = useState<boolean>(true)
    const [dummyList, setDummyList] = useState<string>('')
    const [isMobileFilter, setIsMobileFilter] = useState<boolean>(false)
    const [isMobileShowDetails, setIsMobileShowDetails] = useState<boolean>(false)
    // const [currency, setCurrency] = useState<string>('INR')
    // const [isMobile, setIsMobile] = useState<boolean>(false)
    const [noDataFound, setNoDataFound] = useState<boolean>(false)
    const [flightSearchData, setFlightSearchData] = useState<FormSearch | undefined>()
    const currency = 'INR'
    const isMobile = false
    const helper = useMemo(() => new FlightListHelper(), []);
    const filterHelper = useMemo(() => new FlightFilterHelper(new Date()), []);
    const api = FlightApiService;

    const checkHvrDffrent = useCallback((date1: string, date2: string): boolean => {
        if (date1 === "" && date2 === "") {
          return false;
        }
        const time1 = new Date(date1).getTime();
        const time2 = new Date(date2).getTime();
        const timeDiff = Math.abs(time1 - time2);
        const hoursDiff = timeDiff / (1000 * 3600);
        return hoursDiff >= 3;
      }, []);


    const selectReturnTrip = useCallback((ind: number) => {
        const dl = DisplayListRef.current;
        const st = selectedTripRef.current;
        if (dl && st && dl[1]?.Journey[ind]) {
          const time = checkHvrDffrent(
            st[0]?.Journey?.ArrivalTime || "",
            dl[1]?.Journey[ind]?.DepartureTime || ""
          );
          if (time) {
            dl[1]!.Journey[ind]!.isSelect = true;
            st[1]!.Journey = dl[1]!.Journey[ind];
          } else {
            // Recursive call with the same callback reference
            selectReturnTrip(ind + 1);
          }
        }
      }, [checkHvrDffrent,DisplayListRef,selectedTripRef]);

    const selectinitialTrip = useCallback((): void => {
        const dl = DisplayListRef.current;
        const st = selectedTripRef.current;
        if (dl[0]?.Journey[0]) {
          // Mark the first journey as selected.
          dl[0]!.Journey[0].isSelect = true;
          st[0]!.Journey = dl[0]!.Journey[0];
          setSelectedTrip(st);
          selectReturnTrip(0);
        }
      }, [setSelectedTrip, selectReturnTrip,DisplayListRef,selectedTripRef]);
      
      
    
    const listConvertPagination = useCallback((onList: Journey[], reList: Journey[]): void => {
        const itemsPerPage = 10;
        const ONnumberOfPage = Math.ceil(onList.length / itemsPerPage);
        const REnumberOfPage = Math.ceil(reList.length / itemsPerPage);
      
        const pl: MCPaginateSplitTuis[] = PaginateListRef.current;
        const dl: MCSplitTuis[] = DisplayListRef.current;
      
        // Reset journeys arrays
        pl[0]!.Journey = [];
        dl[0]!.Journey = [];
        pl[1]!.Journey = [];
        dl[1]!.Journey = [];
      
        setNextPageIndex(0);
      
        // Paginate the onList journeys
        for (let i = 0; i < ONnumberOfPage; i++) {
          const startIndex = i * itemsPerPage;
          const endIndex = startIndex + itemsPerPage;
          pl[0]!.Journey.push(onList.slice(startIndex, endIndex));
        }
      
        // Paginate the reList journeys
        for (let i = 0; i < REnumberOfPage; i++) {
          const startIndex = i * itemsPerPage;
          const endIndex = startIndex + itemsPerPage;
          pl[1]!.Journey.push(reList.slice(startIndex, endIndex));
        }
      
        setPaginateList(pl);
      
        // Set initial display list from the first page of each trip
        if (pl[0]!.Journey.length > 0) {
          dl[0]!.Journey = pl[0]!.Journey[0] || [];
        }
        if (pl[1]!.Journey.length > 0) {
          dl[1]!.Journey = pl[1]!.Journey[0] || [];
        }
      
        selectinitialTrip();
        setListShimmer(false);
      }, [
        PaginateListRef, // assumed stable (created via useRef)
        DisplayListRef,  // assumed stable (created via useRef)
        setNextPageIndex,
        setPaginateList,
        selectinitialTrip,
        setListShimmer
      ]);

    const convertListDataToDisplayData = useCallback((): void => {
        let campaignOnList: Journey[] = [];
        let listOn: Journey[] = [];
        let campaignReList: Journey[] = [];
        let listRe: Journey[] = [];
        const lt = listTripRef.current;
        campaignOnList = helper.combineSameFlight(lt[0]!.Journey);
        listOn = campaignOnList?.filter(
          (x) => x.isDisplay === true && x.GrossFare > 0 && x.Seats !== 0
        ) || [];
        campaignReList = helper.combineSameFlight(lt[1]!.Journey);
        listRe = campaignReList?.filter(
          (x) => x.isDisplay === true && x.GrossFare > 0 && x.Seats !== 0
        ) || [];
        if (listOn.length > 0 && listRe.length > 0) {
          listConvertPagination(listOn, listRe);
        }
      }, [helper, listTripRef, listConvertPagination]);

      const convaretListToFilterData = useCallback((): void => {
        let campaignOnList: Journey[] = [];
        let listOn: Journey[] = [];
        let campaignReList: Journey[] = [];
        let listRe: Journey[] = [];
        const lt = listTripRef.current;
        campaignOnList = helper.combineSameFlight(lt[0]!.Journey);
        listOn =
          campaignOnList?.filter(
            (x) => x.isDisplay === true && x.GrossFare > 0 && x.Seats !== 0
          ) || [];
        campaignReList = helper.combineSameFlight(lt[1]!.Journey);
        listRe =
          campaignReList?.filter(
            (x) => x.isDisplay === true && x.GrossFare > 0 && x.Seats !== 0
          ) || [];
        if (listOn.length > 0 && listRe.length > 0) {
          setFilterData([
            {
              From: lt[0]!.From,
              To: lt[0]!.To,
              Tui: "",
              FilterData: filterHelper.setFlightFilterDate(listOn),
            },
            {
              From: lt[1]!.From,
              To: lt[1]!.To,
              Tui: "",
              FilterData: filterHelper.setFlightFilterDate(listRe),
            },
          ]);
          const dum = [
            {
              From: lt[0]!.From,
              To: lt[0]!.To,
              List: listOn,
            },
            {
              From: lt[1]!.From,
              To: lt[1]!.To,
              List: listRe,
            },
          ];
          setDummyList(JSON.stringify(dum));
          setFilterShimmer(false);
          setListShimmer(false);
        } else {
          setNoDataFound(true);
        }
      }, [
        helper,
        filterHelper,
        setFilterData,
        setDummyList,
        setFilterShimmer,
        setListShimmer,
        setNoDataFound,
        listTripRef,
      ]);

    const RTWithTuiExpressSearch: () => Promise<void> = useCallback(async () => {
        const body: ExpressSearchBody = {
          ClientID: "",
          TUI: TUI,
          Source: "ST",
          Mode: "SY",
          FareType: "RT",
        };
      
        try {
          const data = await api.callExpressSearch(body);
          if (data.Code === 200) {
            if (data.Trips && data.Trips[0] && data.Trips[0].Journey) {
              listTrip[0]!.Journey = listTrip[0]!.Journey.concat(
                helper.oneWayJournyKeySet(data.Trips[0].Journey)
              );
            }
            if (data.Trips && data.Trips[1] && data.Trips[1].Journey) {
              listTrip[1]!.Journey = listTrip[1]!.Journey.concat(
                helper.oneWayJournyKeySet(data.Trips[1].Journey)
              );
            }
            convertListDataToDisplayData();
            convaretListToFilterData();
          } else {
            sessionStorage.removeItem("dySearchTuiID");
            // callExpressSearch();
          }
        } catch (error) {
          setNoDataFound(true);
          setListShimmer(false);
        }
      }, [
        TUI,
        api,
        helper,
        listTrip,
        convertListDataToDisplayData,
        convaretListToFilterData,
        // callExpressSearch,
        setNoDataFound,
        setListShimmer,
      ]);
      

      const callGetExpressSearch = useCallback(async (TUI: string): Promise<void> => {
        const body: GetExpressSearchBody = {
          ClientID: '',
          TUI: TUI,
        };
      
        try {
          const data = await api.getExpressSearch(body);
          if (data.Code === 200) {
            const lt = listTripRef.current; // Assuming listTripRef is a ref that remains stable
      
            if (data.Trips && data.Trips[0] && data.Trips[0].Journey) {
              lt[0]!.Journey = lt[0]!.Journey.concat(
                helper.oneWayJournyKeySet(data.Trips[0].Journey)
              );
            }
      
            if (data.Trips && data.Trips[1] && data.Trips[1].Journey) {
              lt[1]!.Journey = lt[1]!.Journey.concat(
                helper.oneWayJournyKeySet(data.Trips[1].Journey)
              );
            }
      
            setListTrip(lt);
            convertListDataToDisplayData();
          }
          if (data.Completed) {
            callGetExpressSearch(TUI);
          }
          if (data.Completed) {
            convaretListToFilterData();
          }
        } catch (error) {
          setListShimmer(false);
          setNoDataFound(true);
        }
      }, [
        api,
        helper,
        listTripRef, // assuming this ref is stable
        setListTrip,
        convertListDataToDisplayData,
        convaretListToFilterData,
        setListShimmer,
        setNoDataFound,
      ]);

      const RTWithoutTuiExpressSearch = useCallback(async (): Promise<void> => {
        const body = scheduleBodySet(flightSearchData, "RT");
        try {
          const data = await api.callExpressSearch(body);
          if (data.Code === 200) {
            setTUI(data.TUI);
            // Use data.TUI here so the updated value is saved.
            sessionStorage.setItem("dySearchTuiID", data.TUI);
            callGetExpressSearch(data.TUI);
          } else {
            setNoDataFound(true);
          }
        } catch (error) {
          setListShimmer(false);
        }
      }, [
        flightSearchData,
        api,
        setTUI,
        callGetExpressSearch,
        setNoDataFound,
        setListShimmer,
      ]);

      const callExpressSearch = useCallback(async () => {
        // Reset state values
        setListTrip([]);
        setTUI('');
        setDisplayList([]);
        setPaginateList([]);
        setNextPageIndex(0);
        setListShimmer(true);
        setSelectedTrip([]);
        setTotalPrice(0);
        setFilterData([]);
        setFilterShimmer(true);
        setDummyList('');
        setNoDataFound(false);
      
        // Process each trip (this part remains the same)
        flightSearchData?.trips.forEach(async (x) => {
          const pushData = [
            {
              From: x.from.iata,
              FromName: x.from.city,
              To: x.to.iata,
              ToName: x.to.city,
              TUI: '',
              Journey: []
            },
            {
              From: x.to.iata,
              FromName: x.to.city,
              To: x.from.iata,
              ToName: x.from.city,
              TUI: '',
              Journey: []
            }
          ];
          setListTrip(pushData);
          setDisplayList(pushData);
          setPaginateList(pushData);
          setSelectedTrip([
            { from: x.from.iata, to: x.to.iata },
            { from: x.to.iata, to: x.from.iata }
          ]);
        });
      
        // Decide which search function to call based on session storage
        if (sessionStorage && sessionStorage.getItem('dySearchTuiID')) {
          setTUI(sessionStorage.getItem('dySearchTuiID') || '');
          RTWithTuiExpressSearch();
        } else {
          RTWithoutTuiExpressSearch();
        }
      }, [
        flightSearchData, 
        RTWithTuiExpressSearch, 
        RTWithoutTuiExpressSearch,
        setDisplayList,
        setPaginateList,
        setListTrip,
        setSelectedTrip
      ]);
      
    useEffect(() => {
        if (getFlightSearchBody) {
            setFlightSearchData(getFlightSearchBody);
            if (flightSearchData) {
                callExpressSearch();
            }
        }
    }, [getFlightSearchBody,flightSearchData,callExpressSearch]);




    const { setFlightDetailsBodyData, setIsFlightDetailsPopup } = useFlightState()


  
    function resetFilter(i: number) {
        const dmmlist: { From: string, To: string, List: Journey[] }[] = JSON.parse(dummyList) as { From: string, To: string, List: Journey[] }[];
        filterData[i]!.FilterData = filterHelper.setFlightFilterDate(dmmlist[i]!.List);
        fliterDataGet();
    }

    function resetFilterAll() {
        const dmmlist: { From: string, To: string, List: Journey[] }[] = JSON.parse(dummyList) as { From: string, To: string, List: Journey[] }[];
        filterData[0]!.FilterData = filterHelper.setFlightFilterDate(dmmlist[0]!.List);
        filterData[1]!.FilterData = filterHelper.setFlightFilterDate(dmmlist[1]!.List);
        setIsMobileFilter(false)
        fliterDataGet();
    }

    const loadNextPage = useCallback(() => {
        const updatedIndex = nextPageIndex + 1 
        setNextPageIndex(updatedIndex)

        PaginateList.forEach((x, i: number) => {
            if (updatedIndex <= x.Journey.length) {
                displayList[i]!.Journey = displayList[i]!.Journey.concat(x.Journey[updatedIndex]!)
            }
        });
    }, [nextPageIndex, PaginateList, displayList, setNextPageIndex]);

    useEffect(() => {
        const handleScroll = () => {
            if (
                window.innerHeight + window.scrollY >=
                Math.max(
                    document.body.scrollHeight,
                    document.body.offsetHeight,
                    document.documentElement.clientHeight,
                    document.documentElement.scrollHeight,
                    document.documentElement.offsetHeight
                )
            ) {
                loadNextPage();
            }
        };

        window.addEventListener('scroll', handleScroll);

        // Cleanup
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, [loadNextPage]);



    function selectFlightCard(tripIndx: number, jrnyIndex: number) {
        const currentJourney = displayList[tripIndx]!.Journey[jrnyIndex];
        const selectedTripBefore = selectedTrip[tripIndx - 1];
        const selectedTripAfter = selectedTrip[tripIndx + 1];

        const time1 = selectedTripAfter
            ? checkHvrDffrent(selectedTripAfter.Journey?.DepartureTime || '', currentJourney!.ArrivalTime || '')
            : false;

        const time2 = selectedTripBefore
            ? checkHvrDffrent(currentJourney!.DepartureTime || '', selectedTripBefore.Journey?.ArrivalTime || '')
            : false;

        if ((tripIndx === 0 && time1) || (tripIndx + 1 === selectedTrip.length && time2) || (time1 && time2)) {
            displayList[tripIndx]!.Journey.forEach((x, i: number) => {
                if (i === jrnyIndex) {
                    displayList[tripIndx]!.Journey[i]!.isSelect = true;
                    selectedTrip[tripIndx]!.Journey = x;
                    setTotalPrice(totalPrice + x.GrossFare)
                } else {
                    displayList[tripIndx]!.Journey[i]!.isSelect = false;
                }
            });
        } else {
            openTimePopUp()
        }
    }

    function openTimePopUp() {
        // dialog.open(FlightTimeDifferentPopupComponent)
    }

    function BookNow() {
        const body = [{
            "Trips": [
                {
                    "Amount": selectedTrip[0]!.Journey?.GrossFare,
                    "Index": selectedTrip[0]!.Journey?.Index,
                    "ChannelCode": null,
                    "OrderID": 1,
                    "TUI": TUI
                },
                {
                    "Amount": selectedTrip[1]!.Journey?.GrossFare,
                    "Index": selectedTrip[1]!.Journey?.Index,
                    "ChannelCode": null,
                    "OrderID": 2,
                    "TUI": TUI
                }
            ],
            "ClientID": "",
            "Mode": "SS",
            "Options": "A",
            "Source": "SF",
            "TripType": "RT"
        }]
        sessionStorage.removeItem('dygPriceListId');
        sessionStorage.removeItem('dySearchTuiID');
        sessionStorage.setItem('dyPricingBody', JSON.stringify(body));
        // route.navigate(['/flight-bookings/payment'])
    }

    function fliterDataGet() {
        const dmmlist: { From: string, To: string, List: Journey[] }[] = JSON.parse(dummyList) as { From: string, To: string, List: Journey[] }[];

        const listOn: Journey[] = filterHelper.addFilterInList(filterData[0]!.FilterData, dmmlist[0]!.List);
        const listRe: Journey[] = filterHelper.addFilterInList(filterData[1]!.FilterData, dmmlist[1]!.List);
        listConvertPagination(listOn, listRe);
    }

    function openFilter() {
        setIsMobileFilter(true)
    }

    function closeFilter() {
        setIsMobileFilter(false)
    }

    function showFlightDetails(k: Journey) {
        const body: FlightDetailsEmitData = {
            TripType: 'ON',
            Trips: [[{
                "Amount": k.GrossFare,
                "ChannelCode": null,
                "Index": k.Index || '',
                "OrderID": 1,
                "TUI": TUI
            }]]
        }
        setFlightDetailsBodyData(JSON.stringify(body))
        setIsFlightDetailsPopup(true)
    }

    // function flightDetailsOnRe() {
    //     const body = {
    //         TripType: 'RT',
    //         "Trips": [
    //             [{
    //                 "Amount": selectedTrip[0]!.Journey?.GrossFare,
    //                 "Index": selectedTrip[0]!.Journey?.Index,
    //                 "ChannelCode": null,
    //                 "OrderID": 1,
    //                 "TUI": TUI
    //             },
    //             {
    //                 "Amount": selectedTrip[1]!.Journey?.GrossFare,
    //                 "Index": selectedTrip[1]!.Journey?.Index,
    //                 "ChannelCode": null,
    //                 "OrderID": 2,
    //                 "TUI": TUI
    //             }]
    //         ]
    //     }
    //     setFlightDetailsBodyData(JSON.stringify(body))
    //     setIsFlightDetailsPopup(true)
    // }

    function showMobDetails() {
        setIsMobileShowDetails(true)
    }

    function hideMobDetails() {
        setIsMobileShowDetails(false)
    }

    return (
        <>
            <div className={`container mx-auto ${styles["round_trip_list_div"]}`}>
                
                {!noDataFound ? (

                    <>
                        <div className={styles["filter_div"]}>
                            <FilterFlight
                                shimmer={filterShimmer}
                                initialFilterData={filterData}
                                emitFilter={fliterDataGet}
                                currency={currency}
                                handleFilterChange={fliterDataGet}
                            />
                        </div>

                        <div className={styles["list_div"]}>
                            <div className={styles["mobile_filter_div"]}>
                                <div className="dy_secondary_bttn" onClick={openFilter}>
                                    <span>Filter</span>
                                </div>
                            </div>

                            {/* Shimmer Loading */}
                            {listShimmer ? (
                                <div className={styles["rt_split_view_list_div"]}>
                                    <div className={styles["card_list_div"]}>
                                    {[...Array(10)].map((_, i) => (
                                        <FlightSplitViewShimmer key={i}/>
                                    ))}
                                    </div>
                                    <div className={styles["card_list_div"]}>
                                    {[...Array(10)].map((_, i) => (
                                        <FlightSplitViewShimmer key={i}/>
                                    ))}
                                    </div>
                                </div>
                            ) : (
                                <div className={styles["rt_split_view_list_div"]}>
                                    {displayList && displayList.length > 0 ? (
                                        displayList.map((trip, tripIndex) => (
                                            <div className={styles["card_list_div"]} key={tripIndex}>
                                                {trip.Journey.length > 0 ?
                                                    trip.Journey.map((journey, journeyIndex) => (
                                                        <FlightSplitViewCard
                                                            key={journeyIndex}
                                                            flight={journey}
                                                            currency={currency}
                                                            isMobile={isMobile}
                                                            selectFlight={() => selectFlightCard(tripIndex, journeyIndex)}
                                                            emitFlightDetails={() => showFlightDetails(journey)}
                                                        />
                                                    )) : (
                                                        <div className="no-data-found-div">
                                                            <div className="main-txt">Sorry no results were found.</div>
                                                            <div>Please try changing your filters.</div>
                                                            <button className="out-line-button" onClick={() => resetFilter(tripIndex)}><RestartAltRounded/> Reset</button>
                                                        </div>
                                                    )
                                                    }
                                            </div>
                                        ))
                                    ):(
                                        <div className="no-data-found-div">
                                            <div className="main-txt">Sorry no results were found.</div>
                                            <div>Please try changing your filters.</div>
                                            <button className="out-line-button" onClick={resetFilterAll}><RestartAltRounded/> Reset</button>
                                        </div>
                                    )}

                                </div>
                            )}
                        </div>
                    </>
                    
                ) : (
                    <div className="no-data-found-div">
                        <div className="main-txt">Sorry, no results were found.</div>
                        <div>Please try changing your cities or dates.</div>
                    </div>
                )}

                {/* Mobile Details Overlay */}
                {isMobileShowDetails && isMobile && (
                    <div className={styles["rt_split_view_select_card_overlay_div"]} onClick={hideMobDetails}></div>
                )}

                {/* Flight Selection Details */}
                {selectedTrip[0]?.Journey && selectedTrip[1]?.Journey && (
                    <div className={`${styles["rt_split_view_select_card_div"]} ${isMobileShowDetails ? styles["show_details_border"] : ''}`} >
                        <div className={`${styles["select_card_data"]} container`} >
                                <div className={styles["onw_ret_card"]}>
                                    <FlightSplitViewCard flight={selectedTrip[0].Journey} isBottomCard={true} />
                                </div>
                                <div className={styles["onw_ret_card"]}>
                                    <FlightSplitViewCard flight={selectedTrip[1].Journey} isBottomCard={true} />
                                </div>
                                <div className={styles["total_price_bttn"]}>
                                    <div className={styles["total_price_div"]}>
                                        {selectedTrip[0].Journey.GrossFare + selectedTrip[1].Journey.GrossFare} {currency}
                                    </div>
                                    <div className={styles["button_details_div"]}>
                                        <button className="dy_primary_bttn" onClick={BookNow}>
                                            Continue
                                        </button>
                                        <div className="linkBtn" onClick={showMobDetails}>
                                            + Flight Details
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className={`${styles["mobile_select_card_data"]} container`}>
                                <div className={styles["price_tot_bttn_div"]}>
                                    <div className={styles["price_div"]}>
                                        <div>
                                            <div className={styles["text_div"]}>Onward</div>
                                            <div className={styles["price_div"]}>
                                                {selectedTrip[0].Journey.GrossFare} {currency}
                                            </div>
                                        </div>
                                        <div>
                                            <div className={styles["text_div"]}>Return</div>
                                            <div className={styles["price_div"]}>
                                                {selectedTrip[1].Journey.GrossFare} {currency}
                                            </div>
                                        </div>
                                    </div>
                                    <div className={styles["total_bttn_div"]}>
                                        <div className={styles["total_amt"]}>
                                            {selectedTrip[0].Journey.GrossFare + selectedTrip[1].Journey.GrossFare} {currency}
                                        </div>
                                        <button className="dy_primary_bttn" onClick={BookNow}>
                                            Continue
                                        </button>
                                    </div>
                                </div>

                                {isMobileShowDetails && (
                                    <div>
                                        <div className={styles["brdr_top_flight"]}>
                                            <FlightSplitViewCard flight={selectedTrip[0].Journey} isBottomCard={true} />
                                        </div>
                                        <div className={styles["brdr_top_flight"]}>
                                            <FlightSplitViewCard flight={selectedTrip[1].Journey} isBottomCard={true} />
                                        </div>
                                    </div>
                                )}
                                {!isMobileShowDetails && (
                                    <div className="linkBtn" onClick={showMobDetails}>
                                        + Flight Details
                                    </div>
                                )}
                            </div>

                        {isMobileShowDetails && (
                            <div className={styles["mobile_close_details_div"]}>
                                <CloseIcon className={styles["icons"]} onClick={hideMobDetails}/>
                            </div>
                        )}
                    </div>
                )}

                {/* Mobile Filter Popup */}
                {isMobile && (
                    <DyBottomTopPanel open={isMobileFilter} heading={'Filter'} close={closeFilter} type={""}>
                        <div className={'mob-flight-filter-view-div'}>
                            {/* <dy-flight-filter [shimmer]="filterShimmer" [filterData]="filterData" [currency]="currency"
                            (emitFilter)="fliterDataGet()"></dy-flight-filter> */}
                            <div className={styles["button-div"]}>
                                <div className={styles["linkstyle"]} onClick={resetFilterAll}>Reset all filters</div>
                                <button className={styles["button-search searchbtn"]} onClick={closeFilter}>Submit</button>
                            </div>
                        </div>
                    </DyBottomTopPanel>
                )}
            </div>


        </>
    )
}
