# Technical Context

## Technology Stack
- **Framework**: Next.js 14+ with TypeScript
- **UI Library**: Material-UI (@mui/icons-material)
- **Styling**: SCSS modules (styles/variable.scss)
- **HTTP Client**: Axios
- **Routing**: Next.js App Router (useRouter from next/navigation)
- **State Management**: React hooks (useState, useEffect, useCallback)
- **Storage**: localStorage for user data persistence

## Project Structure
```
app/
├── page.tsx (main registration page)
├── flight/
│   ├── FlightHomePage/
│   ├── FlightReview/
│   └── FlightSuccess/
services/
├── flight-api-service.tsx
models/
├── flight-search-model.ts
├── flight-booking-models.ts
└── flight-info-ssr-model.ts
styles/
└── variable.scss
```

## API Configuration
- Base URL: https://app.digiyatra.in/apis/
- Content-Type: application/json
- Current endpoints:
  - auth/register/ (POST) - User registration
  - airports/ (POST) - Airport search
  - search/ (POST) - Flight search
  - pricing/ (POST) - Smart pricer
  - create-booking/ (POST) - Booking creation

## Development Patterns
- Functional components with TypeScript
- Custom hooks for API calls
- Error handling with try-catch blocks
- Loading states with boolean flags
- Form validation with required fields
- Responsive design with media queries

## Current Registration API Response
```typescript
{
  message: string // "User registered successfully."
}
```

## Required OTP API Response
```typescript
{
  user_id: number,
  message: string,
  otp_sent: boolean
}
```
