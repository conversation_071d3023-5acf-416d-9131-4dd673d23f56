export interface FlightInfoBody {
    ClientID: string
    TripType: string
    Trips: FLightDetailBodyTrip[]
}

export interface FlightSsrBody {
    ClientID: string
    PaidSSR: boolean
    Source: string
    Trips: FLightDetailBodyTrip[]
}

export interface FlightFareRuleBody {
    ClientID: string
    Source: string
    Trips: FLightDetailBodyTrip[]
}

export interface FlightDetailsEmitData {
    TripType: string
    Trips: FLightDetailBodyTrip[][]
}

export interface FLightDetailBodyTrip {
    TUI: string
    Amount: number
    Index: string
    OrderID: number
    ChannelCode: string | null
}

export interface FlightInfoResponse {
    TUI: string
    From: string
    To: string
    OnwardDate: string
    ReturnDate: string
    ADT: number
    CHD: number
    INF: number
    YTH: number
    NetAmount: number
    SSRAmount: number
    GrossAmount: number
    Trips: FlightInfoTrip[]
    GeneralKeys: string | null
    CeilingInfo: string | null
    Code: string
    Msg: string[]
}

export interface FlightInfoTrip {
    Journey: FlightInfoJourney[]
}

export interface FlightInfoJourney {
    Provider: string
    OrderID: number
    Stops: number
    Index: string
    SPFareNotice: string
    GrossFare: number
    NetFare: number
    Notices: FlightInfoNotice[] | null
    Segments: FlightInfoSegment[]
    FCType: string
}

export interface FlightInfoNotice {
    Notice: string
    Link: string
    NoticeType: string
}

export interface FlightInfoSegment {
    Flight: FlightInfoFlight
    Fares: FlightInfoFares
}

export interface FlightInfoFlight {
    FUID: number
    VAC: string
    MAC: string
    OAC: string
    FBC: string
    Airline: string
    FlightNo: string
    ArrivalTime: string
    DepartureTime: string
    FareClass: string
    ArrivalCode: string
    DepartureCode: string
    ArrivalTerminal: string
    DepartureTerminal: string
    ArrAirportName: string
    DepAirportName: string
    EquipmentType: string
    RBD: string
    Cabin: string
    Refundable: string
    Amenities: string | null
    Seats: number
    Hops: FlightInfoHops[] | null
    Duration: string
    AirCraft: string
}

export interface FlightInfoHops {
    ArrivalTime: string
    DepartureTime: string
    ArrivalCode: string
    ArrAirportName: string
    Duration: string
    ArrivalDuration: string
    DepartureDuration: string
}

export interface FlightInfoFares {
    PTCFare: FlightInfoPtcfare[]
    GrossFare: number
    NetFare: number
    TotalServiceTax: number
    TotalTransactionFee: number
    TotalBaseFare: number
    TotalTax: number
    TotalCommission: number
    TotalVATonServiceCharge: number
    TotalVATonTransactionFee: number
    TotalAgentMarkUp: number
    TotalAddonMarkup: number
    TotalAddonDiscount: number
    TotalAtoCharge: number
    TotalReissueCharge: number
    OldSSRAmount: number
}

export interface FlightInfoPtcfare {
    PTC: string
    Fare: number
    YQ: number
    PSF: number
    YR: number
    UD: number
    K3: number
    K7: number
    API: number
    RCF: number
    RCS: number
    PHF: number
    CUTE: number
    OTT?: string
    OT?: string
    Tax: number
    GrossFare: number
    NetFare: number
    ST: number
    TransactionFee: number
    VATonServiceCharge: number
    VATonTransactionFee: number
    AgentMarkUp: number
    AddonMarkup: number
    ATOAddonMarkup: number
    AddonDiscount: number
    Ammendment: number
    AtoCharge: number
    ReissueCharge: number
    OldSSRAmount: number
    CGST: number
    SGST: number
    IGST: number
    JN?: number
}

export interface FlightSsrResponse {
    TUI: string
    CurrencyCode: string
    PaidSSR: boolean
    Trips: FlightSsrTrip[]
    Code: string
    Msg: string[]
}

export interface FlightSsrTrip {
    From: string
    To: string
    Journey: FlightSsrJourney[]
}

export interface FlightSsrJourney {
    Provider: string
    MultiSSR: string
    ConversationID: string
    Segments: FlightSsrSegment[]
}

export interface FlightSsrSegment {
    FUID: string
    VAC: string
    Index: string | null
    SSR: FlightSsrSsr[]
}

export interface FlightSsrSsr {
    Code: string
    Description: string
    PieceDescription: string
    Charge: number
    VAT: number
    Type: string
    Category: string
    PTC: string
    ID: number
    IsFreeMeal: boolean
    MealImage: string
    SSRUrl: string | null
    AdditionalFields: string | null
}

export interface FlightFareRuleResponse {
    TUI: string
    Code: string
    Msg: string[]
    CurrencyCode: string
    Trips: FlightFareRuleTrip[]
}

export interface FlightFareRuleTrip {
    Journey: FlightFareRuleJourney[]
}

export interface FlightFareRuleJourney {
    Provider: string
    Segments: FlightFareRuleSegment[]
}

export interface FlightFareRuleSegment {
    FUID: string
    VAC: string
    Rules: FlightFareRuleRule[]
}

export interface FlightFareRuleRule {
    OrginDestination: string
    FareRuleText: string | null
    Rule: FlightFareRuleRule2[]
}

export interface FlightFareRuleRule2 {
    Info: FlightFareRuleInfo[]
    Head: string
}

export interface FlightFareRuleInfo {
    AdultAmount: string
    ChildAmount: string
    InfantAmount: string
    YouthAmount: string | null
    Description: string
    CurrencyCode: string
}

export interface FlightInfoMap {
    from: string
    to: string
    Notices: FlightInfoNotice[] | null
    segments: FlightInfoMapSegment[]
}

export interface FlightInfoMapSegment {
    isHops: boolean
    isConnection: boolean
    connectionAirport: string
    connectionCode: string
    connectionTime: string
    Mac: string
    Airline: string
    FlightNo: string
    AirCraft: string
    Cabin: string
    DepartureTime: string
    DepartureCode: string
    DepAirportName: string
    DepartureTerminal: string
    Duration: string
    ArrivalTime: string
    ArrivalCode: string
    ArrAirportName: string
    ArrivalTerminal: string
}

export interface FareSummaryDetails {
    baseFare: FareSummaryBaseFare
    taxs: FareSummaryTaxs
    addons: FareSummaryTaxs
    totalWithotAddons: number
    totalAmount: number
}

export interface FareSummaryBaseFare {
    title: string
    total: number
    subFare: FareSummarySubBaseFare[]
}

export interface FareSummarySubBaseFare {
    title: string
    price: number
    id: string
    count: number
    totalPrice: number
}

export interface FareSummaryTaxs {
    title: string
    total: number
    subFare: FareSummaryTaxSubFare[]
}

export interface FareSummaryTaxSubFare {
    title: string
    totalPrice: number
}

export interface FlightBaggageInfo {
    from: string
    to: string
    checkin: BaggageInfoCheckinCabin
    cabin: BaggageInfoCheckinCabin
}

export interface BaggageInfoCheckinCabin {
    adult: string
    child: string
    infant: string
}