@import '../../../../../styles/variable.scss';

.card-list-container{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 50px;
    .selected-card-container{
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 15px;
        h2{
            font-size: 18px;
            font-weight: 500;
            span{
                font-size: 14px;
                padding: 3px 5px;
                border-radius: 3px;
                color: #fff;
                background-color: #525252;
                margin-right: 5px;
            }
        }
        .card-preview-container {
            transition: opacity 0.5s ease, transform 0.5s ease , height 0.5s ease;
            transform-origin: top;
            height: 0;
            &.closed {
              opacity: 0;
              height: 0;
              transform: scaleY(0);
            }
            &.open {
              height: auto;
              opacity: 1;
              transform: scaleY(1);
            }
          }
        .selected-content {
          margin-top: 10px;
          opacity: 0;
          height: 0;
          transform: scaleY(0);
          transform-origin: top;

          &.open {
            height: auto;
            opacity: 1;
            transform: scaleY(1);
            transition: opacity 0.5s ease, transform 0.5s ease , height 0.5s ease;
          }
            &:not(.open) {
              transition: none;
            }

            @media screen and (max-width: $isMobile){
              overflow: hidden;
              transform: none;
              transform-origin: none;
              &.open{
                transform: none;
                overflow: auto;
              }
            }
          }

        .total-price-div{
          width: 100%;
          display: flex;
          justify-content: end;
          opacity: 0;
          transition: opacity 0.5s ease;
          &.visible{
            opacity: 1;
          }
          &.hidden{
            opacity: 0;
          }
          .total-container {
            display: flex;
            justify-content: end;
            align-items: center;
            margin-top: 20px;
            padding: 10px;
            gap: 100px;
            background: #f8f9fa; // Light background
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            .total-box {
              border-radius: 8px;
              padding: 10px 20px;
              display: flex;
              flex-direction: column;

              .total-label {
                font-size: 14px;
                color: #6c757d;
              }

              .total-amount {
                font-size: 24px;
                font-weight: bold;
                color: $button_color; // Highlight color
              }
            }

            .button-group {
              display: flex;
              align-items: center;
              gap: 15px;

              .linkBtn {
                font-size: 14px;
                color: $primary_color;
                cursor: pointer;
                text-decoration: underline;

                &:hover {
                  color: darken($primary_color, 10%);
                }
              }
            }
          }
        //   .total_price_bttn {
        //     width: 30%;
        //     display: flex;
        //     align-items: center;
        //     justify-content: space-between;

        //     .total_price_div {
        //         span{
        //           color:  rgba(0, 0, 0, 0.6);;
        //         }
        //         font-size: 25px;
        //         font-weight: 700;
        //         color: #000;
        //         padding: 0 15px;
        //     }

        //     .button_details_div {
        //         display: flex;
        //         flex-direction: column;
        //         place-content: center flex-start;
        //         align-items: center;
        //         gap: 5px;
        //     }
        // }
        }


    }
}