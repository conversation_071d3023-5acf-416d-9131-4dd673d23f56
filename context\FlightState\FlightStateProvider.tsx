import React, { useState, ReactNode } from 'react';
import { FlightStateContext } from './FlightStateContext';
import { scheduleSave } from 'models/flight-list-response-model';

// Define the Props for the Provider
interface FlightStateProviderProps {
    children: ReactNode;
  }
  
  // FlightStateProvider component
  export const FlightStateProvider: React.FC<FlightStateProviderProps> = ({ children }) => {
    const [isFlightDetailsPopup, setIsFlightDetailsPopup] = useState<boolean>(false);
    const [flightDetailsBodyData, setFlightDetailsBodyData] = useState<any>(null);
    const [flightSchedule, setFlightSchedule] = useState<scheduleSave[]>([]);
  
    // Memoized context value
    const value = {
      isFlightDetailsPopup,
      setIsFlightDetailsPopup,
      flightDetailsBodyData,
      setFlightDetailsBodyData,
      flightSchedule,
      setFlightSchedule,
    };
  
    return (
      <FlightStateContext.Provider value={value}>
        {children}
      </FlightStateContext.Provider>
    );
  };
  