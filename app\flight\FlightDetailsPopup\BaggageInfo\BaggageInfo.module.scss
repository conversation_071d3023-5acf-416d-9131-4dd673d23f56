@import '../../../../styles/variable.scss';

.flight-details-baggage-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    place-content: stretch flex-start;
    align-items: stretch;

    .table-brdr-div {
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        place-content: stretch flex-start;
        align-items: stretch;
        display: flex;
        flex-direction: column;

        .column-tbl-div {
            display: flex;
            flex-direction: row;
            place-content: stretch space-between;
            align-items: stretch;
            border-top: 1px solid #d0d0d0;
        }

        .table-head-clmn {
            border-top: none;
            background-color: $bg_light;
            border-radius: 4px 4px 0 0;
        }

        .sector-div {
            display: flex;
            align-items: center;
            width: 32%;
            height: auto;
            padding: 10px;
        }

        .bagage-div {
            display: flex;
            align-items: center;
            width: 32%;
            height: auto;
            padding: 10px;
            border-left: 1px solid #d0d0d0;
        }

        h3 {
            font-size: 14px;
            font-weight: 500;
            color: $text_color;
            margin-bottom: 0;
        }

        p {
            font-size: 13px;
            font-weight: 400;
            margin-bottom: 0;
            color: #000;
        }
    }

    .text-rules-div {
        font-size: 12px;

        .text-icon {
            display: flex;
            gap: 10px;
            color: #5e5e5e;

            .icon-clr {
                color: $button_color;
                padding-top: 4px;
            }
        }
    }

    .dainger-info-div {
        color: $error_color;
        background-color: $light_error_color;
        border-radius: 4px;
        padding: 10px;
        font-size: 12px;
    }
}