@import '../../../../../styles/variable.scss';
.one-way-flight-list-div {
    display: flex;
    gap: 20px;
    padding-bottom: 50px;

    .filter-div {
        width: 22%;
        position: sticky;
        top: 0;
        max-height: 100vh;
        overflow-y: hidden;
        overflow-x: hidden;
        margin-bottom: 50px;
        border: 1px solid #ccc;
        background-color: white;
        border-radius: 4px;

        &:hover {
            overflow-y: auto;
        }
    }

    .list-div {
        width: calc(78% - 20px);
        height: 100%;
        flex-direction: column;
        box-sizing: border-box;
        display: flex;
        place-content: stretch flex-start;
        align-items: stretch;
        max-width: 100%;
        gap: 10px;
    }
}

@media only screen and (max-width: 950px) {
    .one-way-flight-list-div {
        display: block;
        padding-bottom: 50px;

        .filter-div {
            display: none;
        }

        .list-div {
            width: 100%;
        }


    }
}



.mob-flight-filter-view-div {
    height: calc(100vh - 50px);
    overflow: auto;
    padding-bottom: 60px;
    position: relative;

    .button-div {
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: flex-end;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 10px;
        background-color: #fff;
        z-index: 99;
        box-shadow: 0 3px 9px #0006;

        .searchbtn {
            width: fit-content;
        }
    }
}

.filter-div::-webkit-scrollbar {
    width: 4px; 
}

.filter-div::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 100vw; 
}

.filter-div::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 100vw;
}

.filter-div::-webkit-scrollbar-thumb:hover {
    background-color: #9a9a9a; 
}

/* Hide Scrollbar Buttons (Up/Down Arrows) */
.filter-div::-webkit-scrollbar-button {
    display: none; /* Completely remove arrows */
    width: 0;
    height: 0;
}