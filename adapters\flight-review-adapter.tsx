interface TravellerForm {
  paxType: string;
  paxHead: string;
  Title: string;
  FName: string;
  LName: string;
  DOB: string;
  Nationality: string;
  PDOE: string;
  PDOI: string;
  PLI: string;
  Pancard: string;
  PassportNo: string;
  VisaType: string;
}

interface TravellerBookingForm {
  phone_code: string;
  phone: string;
  email: string;
  travellers: TravellerForm[];
}

class FlightReviewPageAdapter {
  // Initialize the form with useForm and set up fields
  travellerBookingForm():{
    phone_code: string,
    phone: string,
    email: string,
    travellers: any[],
  } {
 
    return {
      phone_code: '',
      phone: '',
      email: '',
      travellers: [],
    };
  }

  // Create a single traveler form group
  paxForm(type: string, paxhd: string) {
    return {
      paxType: type,
      paxHead: paxhd,
      Title: '',
      FName: '',
      LName: '',
      DOB: '',
      Nationality: '',
      PDOE: '',
      PDOI: '',
      PLI: '',
      Pancard: '',
      PassportNo: '',
      VisaType: '',
    };
  }

  // Initialize the traveler form set with counts of different traveler types
  paxFormSet(adt: number, chd: number, inf: number) {
    const control = this.travellerBookingForm();

    const travelerTypes = ['A', 'C', 'I'];
    travelerTypes.forEach((type) => {
      const count = type === 'A' ? adt : type === 'C' ? chd : inf;
      for (let i = 1; i <= count; i++) {
        const travelerType = type === 'A' ? 'Adult' : type === 'C' ? 'Child' : 'Infant';
        control.travellers.push(this.paxForm(type, `${travelerType} ${i}`));
        // append(this.paxForm(type, `${travelerType} ${i}`));
      }
    });

    return control
  }

  // Set validation for traveler form based on server response
  setValidationPaxForm(res: any[], form: any) {
    const checklist: any = {
      Nationality: 0,
      VisaType: 0,
      PDOE: 0,
      PLI: 0,
      PassportNo: 0,
      DOB: 0,
      PDOI: 0,
      Pancard: 0,
    };

    res.forEach((x) => {
      for (const key in checklist) {
        if (x[key] === 1) {
          checklist[key] = 1;
        }
      }
    });

    const fieldsToValidate = [
      'DOB',
      'Nationality',
      'PDOE',
      'PDOI',
      'PLI',
      'Pancard',
      'PassportNo',
      'VisaType',
    ];
    
    // const formValues = form.getValues();

    // form.travellers.forEach((traveller: any, index: number) => {
    //   fieldsToValidate.forEach((field) => {
    //     if (checklist[field] === 1) {
    //       form.setValue(`travellers.${index}.${field}`, '', {
    //         shouldValidate: true,
    //       });
    //     }
    //   });
    // });

    return { form, checklist };
  }
}

export default FlightReviewPageAdapter;
