@import '../../styles/variable.scss';

.dy-header-div{
    position: relative;
    z-index: 99;
    height: 60px;
    // box-shadow: 0px 1px 13px #05050533;
    // border-bottom: 1px solid #10010154;
    .inner-header-data{
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 60px;
        a{
            text-decoration: none;
            font-weight: 800;
            font-size: 25px;
            color: #fff;
            img{
                height: 50px;
                object-fit: contain;
            }
        }
        .signin-register-div{
            display: flex;
            align-items: center;
            gap: 10px;
        }
    }
    .dy-secondary-bttn {
        padding: 10px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        font-size: 15px;
        font-weight: 500;
        line-height: 1;
        transition-duration: 0.4s;
        background: #fff;
        color: $button_color;
        border-radius: 5px;
        outline: 0;
        border: 1px solid #fff;
        min-width: 100px;
        cursor: pointer;
    }

    .logotxt{
        color: $primary_color;
        font-weight: 800;
        font-size: 25px;
    }

    // Profile dropdown styles
    .profile-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .profile-icon {
        display: flex;
        align-items: center;
        gap: 5px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 8px;
        transition: background-color 0.3s ease;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
    }

    .profile-avatar {
        font-size: 32px !important;
        color: $primary_color;
    }

    .dropdown-arrow {
        font-size: 20px !important;
        color: $primary_color;
        transition: transform 0.3s ease;

        &.rotate {
            transform: rotate(180deg);
        }
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid $bordercolor;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        min-width: 180px;
        z-index: 1000;
        overflow: hidden;
        margin-top: 8px;

        &::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid white;
        }
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
        color: $text_color;
        font-size: 14px;
        font-weight: 500;

        &:hover {
            background-color: $bg_light_color;
        }

        &:not(:last-child) {
            border-bottom: 1px solid $bordercolor;
        }
    }

    .dropdown-icon {
        font-size: 20px !important;
        color: $primary_color;
    }
}