import { scheduleSave } from 'models/flight-list-response-model';
import React, { createContext, useContext } from 'react';

// Define the context type
interface FlightStateContextType {
  isFlightDetailsPopup: boolean;
  setIsFlightDetailsPopup: React.Dispatch<React.SetStateAction<boolean>>;
  flightDetailsBodyData: any;
  setFlightDetailsBodyData: React.Dispatch<React.SetStateAction<any>>;
  flightSchedule: scheduleSave[];
  setFlightSchedule: React.Dispatch<React.SetStateAction<scheduleSave[]>>;
}

// Create the context
export const FlightStateContext = createContext<FlightStateContextType | undefined>(undefined);

// Custom hook to use FlightStateContext
export const useFlightState = (): FlightStateContextType => {
  const context = useContext(FlightStateContext);
  if (!context) {
    throw new Error('useFlightState must be used within a FlightStateProvider');
  }
  return context;
};
