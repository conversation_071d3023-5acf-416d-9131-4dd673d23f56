// airport search body
export interface AirportSearchBody {
    search_text: string
}

export interface AirportListResponse {
    Airports: AirportList[]
    Code: string
    Msg: string[]
}

export interface AirportList {
    city_code: string
    city_name: string
    code: string
    country: string
    id: string
    name: string
}

export interface ScheduleBody {
    SecType: string
    FareType: string
    ADT: number
    CHD: number
    INF: number
    Cabin: string
    Source: string
    Mode: string
    ClientID: string
    IsMultipleCarrier: boolean
    IsRefundable: boolean
    preferedAirlines: string | null
    TUI: string
    Trips: Trip[]
    Parameters: Parameters
    PaymentType?: string
    YTH: number
}

export interface Trip {
    From: string
    FromArptName?: string
    FromCity?: string
    OnwardDate: string | null
    OrderId?: number
    ReturnDate?: string | null
    To: string
    ToArptName?: string
    ToCity?: string
    TUI: string
}

export interface Parameters {
    Airlines: string
    GroupType: string
    Refundable: string
    IsDirect: boolean
    IsStudentFare: boolean
    IsNearbyAirport: boolean
}

export interface TripsForm {
    from: FromToForm
    to: FromToForm
    depart: Date
    return: Date | undefined
}

export interface FromToForm {
    city: string
    airport: string
    iata: string
    country: string
    airportOpen: boolean
}

export interface FormSearch {
    FareType: string;
    travellers: Travellers;
    cabin: string;
    SecType: string;
    trips: TripsForm[];
}

export interface Travellers {
    adult: number
    child: number
    infant: number
}

export interface ExpressSearchBody {
    ClientID: string
    TUI: string | null
    Source: string
    Mode: string
    FareType?: string
}

export interface GetExpressSearchBody {
    ClientID: string
    TUI: string | null
}

export interface TravellersForm {
    adult: number
    child: number
    infant: number
    travellerClass: string
}

export interface WebSettings {
    Code: string
    Msg: string[]
    TUI: string
    Settings: Setting[]
}

export interface Setting {
    Key: string
    Value?: string
}