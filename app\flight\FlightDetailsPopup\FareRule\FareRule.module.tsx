"use client";

import { FlightFareRuleTrip } from 'models/flight-info-ssr-model';
import styles from './FareRule.module.scss';
import React from 'react';
import { InfoTwoTone } from '@mui/icons-material';
interface FareRuleComponentProp{
  fareRuleList: FlightFareRuleTrip[];
  currency: string;
  adult: number;
  child: number;
  infant: number;
}

export function FareRuleComponent({fareRuleList = [], currency = 'INR', adult = 1, child = 0,infant = 0}:FareRuleComponentProp) {

  function FlightCurrencyConverter(InfantAmount: string): React.ReactNode {
   return InfantAmount
  }

  return (
    <>

      <div className={styles["flight-details-fare-rule-div"]}>
        <div className={styles["head-fare-rule-div"]}>Fare Rules</div>
        <div className={styles["flight-details-fare-rule-card"]}>
          {fareRuleList.map((k, i) =>
            k.Journey.map((jer, j) =>
              jer.Segments.map((seg, s) =>
                seg && seg.Rules && seg.Rules.length > 0 && seg.Rules.map((rule, r) => (
                  <React.Fragment key={`${i}-${j}-${s}-${r}`}>
                    <div className={styles["column-data-div"]}>
                      <div className={styles["label-div"]}>
                        <h2>{rule.OrginDestination}</h2>
                      </div>
                      {adult > 0 && rule.Rule.length > 0 && <div className={styles["value-txt"]}><h3>Adult</h3></div>}
                      {child > 0 && rule.Rule.length > 0 && <div className={styles["value-txt"]}><h3>Child</h3></div>}
                      {infant > 0 && rule.Rule.length > 0 && <div className={styles["value-txt"]}><h3>Infant</h3></div>}
                    </div>

                    {rule.Rule.length > 0 ? (
                      rule.Rule.map((ru, idx) => (
                        <React.Fragment key={idx}>
                          <div className={styles["column-full-data"]}>{ru.Head}</div>
                          {ru.Info.map((info, infoIdx) => (
                            <div className={styles["column-data-div"]} key={infoIdx}>
                              <div className={styles["label-div"]}>
                                <p>{info.Description}</p>
                              </div>
                              {adult > 0 && (
                                <div className={styles["value-txt"]}>
                                  {info.AdultAmount && +info.AdultAmount ? (
                                    <p>{FlightCurrencyConverter(info.AdultAmount)}</p>
                                  ) : (
                                    <p>{info.AdultAmount}</p>
                                  )}
                                </div>
                              )}
                              {child > 0 && (
                                <div className={styles["value-txt"]}>
                                  {info.ChildAmount && +info.ChildAmount ? (
                                    <p>{FlightCurrencyConverter(info.ChildAmount)}</p>
                                  ) : (
                                    <p>{info.ChildAmount}</p>
                                  )}
                                </div>
                              )}
                              {infant > 0 && (
                                <div className={styles["value-txt"]}>
                                  {info.InfantAmount && +info.InfantAmount ? (
                                    <p>{FlightCurrencyConverter(info.InfantAmount)}</p>
                                  ) : (
                                    <p>{info.InfantAmount}</p>
                                  )}
                                </div>
                              )}
                            </div>
                          ))}
                        </React.Fragment>
                      ))
                    ) : rule.FareRuleText ? (
                      <div className={styles["column-full-text-data"]}>
                        <div>{rule.FareRuleText}</div>
                      </div>
                    ) : null}
                  </React.Fragment>
                ))
              )
            )
          )}

          <div className={styles["text-rules-div"]}>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>
                The above data is indicative; fare rules are subject to change by the airline, depending on the fare class,
                and change/cancellation fees may vary based on fluctuations in currency conversion rates.
              </span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>Although we will try to keep this section updated regularly.</span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>Feel free to call our Contact Centre for exact cancellation/change fee.</span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles[" icon-clr"]}><InfoTwoTone/></span>
              <span>Cancellation/Date change requests will be accepted 30 hrs prior to departure.</span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles[" icon-clr"]}><InfoTwoTone/></span>
              <span>GST charges applicable on cancellation penalty.</span>
            </div>
          </div>
        </div>
      </div>

    </>
  );
}