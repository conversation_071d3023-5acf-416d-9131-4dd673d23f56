import { Journey } from 'models/flight-list-response-model';
import React from 'react'
import styles from './selectCard.module.scss';
import { FlightLand, FlightTakeoff, KeyboardArrowDown, Spa, Update } from '@mui/icons-material';
import { Span } from 'next/dist/trace';

interface props{
    handleClick: VoidFunction;
    data: Journey | undefined;
    isOpen: boolean;
    isLoading: boolean;
    isMobile:boolean
}
const SelectedCardPreview: React.FC<props> = ({handleClick , data , isOpen , isLoading , isMobile }) => {

  return (
    <div className={`${styles['selected-flight-preview-card']}`} onClick={handleClick}>
        {!isMobile && (
            isLoading ? (
                <span className={`${styles['loader-span']} shine`}/>
            ): (
                <div className={`${styles['airline-name']}`}>{data?.MAC} - {data?.FlightNo}</div>
            )
        )}
        <div className={`${styles['time-duration-div']}`}>
            <div className={`${styles['icon-txt']}`}>
                <FlightTakeoff/>
                {isLoading ? (
                    <span className={`${styles['loader-span']} shine`}/>
                ):(
                    <span>{new Date(data?.DepartureTime || '').toLocaleTimeString([],{ hour: '2-digit', minute: '2-digit' })}</span>                    
                )}
            </div>
            <div className={`${styles['divider']}`}></div>
            <div className={`${styles['icon-txt']}`}>
                <Update/>
                {isLoading ? (
                    <span className={`${styles['loader-span']} shine`}/>
                ):(
                    <span>{data?.Duration.slice(0,2)} H {data?.Duration.slice(4,6)} M</span>
                )}
            </div>
            <div className={`${styles['divider']}`}></div>
            <div className={`${styles['icon-txt']}`}>
                <FlightLand/>
                {isLoading ? (
                    <span className={`${styles['loader-span']} shine`}/>
                ):(
                    <span>{new Date(data?.ArrivalTime || '').toLocaleTimeString([],{ hour: '2-digit', minute: '2-digit' })}</span>
                )}
            </div>
        </div>
        {!isMobile && (
            <div className={`${styles['price-button-container']}`}>
                {isLoading ? (
                        <span className={`${styles['loader-span']} shine`}/>
                ):(
                    <div className={`${styles['price-div']}`}>&#8377; {data?.GrossFare}</div>
                )}
                <KeyboardArrowDown className={`arrow-icon ${isOpen ? 'rotate' : ''}`} />
            </div>
        )}

    </div>
  )
}

export default SelectedCardPreview