@import '../../../styles/variable.scss';

.flight-list-div {
    position: relative;


    .head-ribben-div {
        width: 100%;
        background: $secondary_color;
        padding: 10px 0;
        margin-bottom: 10px;
        color: $text_color;
        
        h1 {
            font-size: 18px;
            line-height: 18px;
            margin-bottom: 0;
        }
    }


    .header-data-div {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .mob-modi-txt{
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mob-icon{
            display: none;
            @media only screen and (max-width: 950px) {
                // display: block;
                // font-size: 18px;
            }
        }

        .modify-bttn {
            font-size: 16px;
            border: 1px solid $button_color;
            color: $button_color;
            padding: 5px 10px;
            border-radius: 5px;
        }

        .airport-list{
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .right-arrow{
            transform: rotate(180deg);
        }

        h1{
            margin-bottom: 5px;
        }

        .divider-bar:last-child{
            display: none;
        }

        .detail-search-data{
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            font-weight: 400;
        }
    }

    .modify-close-bttn {
        position: relative;
        height: 23px;

        .mody-cl {
            position: absolute;
            z-index: 99;
            background-color: #ffff;
            border-radius: 10px;
            border: 1px solid $primary_color;
            color: $button_color;
            padding: 5px;
            font-size: 30px;
            right: 10px;
            bottom: 10px;
            cursor: pointer;
        }
    }

    .modify-popup-div {
        position: relative;

        .all-bg {
            position: fixed;
            z-index: 98;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            background-color: #00000048;
        }

        .pop-up-div {
            position: fixed;
            z-index: 99;
            left: 0;
            right: 0;

            @media only screen and (max-width: 768px) {
                top: 35px;
            }

            .pop-up-brdr {
                width: 100%;
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .rt-container {
        padding: 0;
    }
}