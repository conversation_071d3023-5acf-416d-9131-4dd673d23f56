"use client"

import { useEffect, useRef, useState } from "react"
import { useFieldArray , useForm  } from 'react-hook-form';
import { AirportList, TravellersForm, TripsForm } from "models/flight-search-model"
import DyAirportSelection from "../DyAirportSelection/DyAirportSelection";
import styles from './DyFlightSearchSection.module.scss'
import Loop from "@mui/icons-material/Loop"
import People from "@mui/icons-material/People"
import DyTravellerClass from "../DyTravellerClass/DyTravellerClass";
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format } from 'date-fns';
import { DateRange, Today } from '@mui/icons-material'
import DyBottomUpPopup from "components/DyBottomUpPopup/DyBottomUpPopup";
import DyBottomTopPanel from "components/DyBottomTopPopup/DyBottomTopPopup";
import FlightTakeoffIcon from '@mui/icons-material/FlightTakeoff';
import FlightLandIcon from '@mui/icons-material/FlightLand';
import FlightIcon from '@mui/icons-material/Flight';
import LanguageIcon from '@mui/icons-material/Language';
import DoneIcon from '@mui/icons-material/Done';
import SearchIcon from '@mui/icons-material/Search';
import formatDate from "helpers/date-helper";
import BottomToTopPopup from "components/BottomToTopPopup/BottomToTopPopup";
import BottomUpPopup from "components/BottomUpPopup/BottomUpPopup";
import { useGeneralState } from "context/GeneralState/GeneralStateContext";
import RefreshIcon from '@mui/icons-material/Refresh';

interface Trip {
    from: {
      city: string;
      airport: string;
      iata: string;
      country: string;
      airportOpen: boolean;
    };
    to: {
      city: string;
      airport: string;
      iata: string;
      country: string;
      airportOpen: boolean;
    };
    depart: Date;
    return: Date | null; 
  }
  
  interface SearchForm {
    FareType: string;
    travellers: {
      adult: number;
      child: number;
      infant: number;
    };
    cabin: string;
    SecType: string;
    trips: Trip[];
  }

export default function DyFlightSearchSection({ searchEmit = () => { }, isModify = false }) {
    const [isTravelPopUpOpen, setIsTravelPopUpOpen] = useState<boolean>(false)
    const [paxCount, setPaxCount] = useState<number>(1)
    const [toDay, setToDay] = useState<Date>(new Date())
    const [submitSignal, setSubmitSignal] = useState(false);
    const [showMobileAirport, setShowMobileAirport] = useState<boolean>(false)
    const [selectAirportindex, setSelectAirportindex] = useState<number>(0)
    const [selectAirportFromTo, setSelectAirportFromTo] = useState<string>('')
    const [selectedAirportIata, setSelectedAirportIata] = useState<any>()
    const [returnDateValue, setReturnDateValue] = useState<Date | null>(null);
    const {isUnder950} = useGeneralState();
    useEffect(() => {
        if (submitSignal) {
            setSubmitSignal(false);
        }
    }, [submitSignal]);

    const { control, watch, setValue, getValues, trigger } = useForm<SearchForm>({
        defaultValues: {
            FareType: 'ON',
            travellers: {
                adult: 1,
                child: 0,
                infant: 0,
            },
            cabin: 'E',
            SecType: 'D',
            trips: [
                {
                    from: {
                        city: 'New Delhi',
                        airport: 'Delhi International Airport',
                        iata: 'DEL',
                        country: 'India',
                        airportOpen: false,
                    },
                    to: {
                        city: 'Mumbai',
                        airport: 'Mumbai International Airport',
                        iata: 'BOM',
                        country: 'India',
                        airportOpen: false,
                    },
                    depart: new Date(),
                    return: null,
                },
            ],
        },
    });


    const { fields, append, remove } = useFieldArray({
        control,
        name: 'trips',
    });

    const returnDatePickerDivRef = useRef<HTMLInputElement | null>(null);
    const departDatePickerRef = useRef(null);

    const formValues = watch();

    useEffect(() => {
        scheduleCall();
    }, [formValues]);

    useEffect(()=>{
        initialCall()
    },[])

    
    function initialCall() {
        const seachValue = localStorage.getItem('dySearchSectorData');
        if (seachValue != null) {
          const searchData = JSON.parse(seachValue);
          setFormValuePatch(searchData);
        } else {
          scheduleCall()
        }
    }

    function setFormValuePatch(searchData:any){
       try{
        setPaxCount(searchData.travellers.adult + searchData.travellers.child + searchData.travellers.infant)
        setValue('FareType',searchData.FareType)
        setValue('cabin',searchData.cabin)
        setValue('SecType',searchData.SecType)
        setValue('travellers.adult',searchData.travellers.adult)
        setValue('travellers.child',searchData.travellers.child)
        setValue('travellers.infant',searchData.travellers.infant)
        remove(0);
        searchData.trips.forEach((x:any) => {
        let depDate = new Date(x.depart);
        let retDate = x.return ? new Date(x.return) : null;
        let toDay = new Date();
        if(depDate < toDay){
            depDate = new Date(toDay);
            retDate = new Date(depDate.getTime() + 24 * 60 * 60 * 1000);
        }
        append({
            from: {
                city: x.from.city,
                airport: x.from.airport,
                iata: x.from.iata,
                country: x.from.country,
                airportOpen: false,
            },
            to: {
                city: x.to.city,
                airport: x.to.airport,
                iata: x.to.iata,
                country: x.to.country,
                airportOpen: false,
            },
            
            depart: depDate,
            return: retDate? retDate : addOneDayIndate(depDate),
        });
        setDepartDate(new Date(depDate))
        if(retDate){
            setReturnDateValue(new Date(retDate))
        }
    })
       }catch(error){
        console.log('error',error)
       }
}
 


const tripArrayGet = fields;

    const addTrip = (x: any, depDate: any, retDate: any) => {
        append({
            from: {
                city: x.from.city,
                airport: x.from.airport,
                iata: x.from.iata,
                country: x.from.country,
                airportOpen: false,
            },
            to: {
                city: x.to.city,
                airport: x.to.airport,
                iata: x.to.iata,
                country: x.to.country,
                airportOpen: false,
            },
            depart:  new Date(depDate),
            return:  new Date(retDate),

        });
        setDepartDate(new Date(depDate))
        setReturnDateValue(new Date(retDate))
    };



    function changeSearchType(value: string) {
        let formValue = getValues()
        setValue('FareType', value);
        if (value == 'ON') {
            if (formValue && formValue.trips && formValue.trips != null) {
                formValue.trips.forEach((element, i) => {
                    if (i != 0) {
                        remove(1)
                    } else {
                        setValue(`trips.${i}.return`, null);
                    }
                });
            }
        }
        if (value == 'RT') {
            if (formValue.trips) {
                formValue.trips.forEach((element, i) => {
                    if (i !== 0) {
                        remove(1)
                    } else {
                        setValue(`trips.${i}.return`, null);
                        const currentDepartDate = getValues(`trips.${i}.depart`)
                        if (currentDepartDate) {
                            const nextDate = addOneDayIndate(currentDepartDate)
                            setValue(`trips.${i}.return`, nextDate);
                            if(nextDate){
                                setReturnDateValue(nextDate)
                            }
                        }
                    }
                });
            }
        }
        if (value == 'MC') {
            if (formValue.trips) {
                formValue.trips.forEach((element, i) => {
                    if (i != 0) {
                        remove(i)
                    } else {
                        setValue(`trips.${i}.return`, null);
                        trigger(`trips.${i}.return`);
                    }
                    addNewMultyCity()
                });
            }
        }
        updateSecType(getValues());
    }

    function openTravelerAdd() {
        setIsTravelPopUpOpen(true)
    }

    function changeTravelClass(evt: TravellersForm) {
        setPaxCount(evt.adult + evt.child + evt.infant)
        setValue('cabin', evt.travellerClass)
        setValue('travellers.adult', evt.adult)
        setValue('travellers.child', evt.child)
        setValue('travellers.infant', evt.infant)

        setTimeout(() => {
            setIsTravelPopUpOpen(false)
        }, 100)
    }

    function addNewMultyCity() {
        const formValue = getValues();
        if (formValue.trips) {
            const tripValue = formValue.trips[formValue.trips.length - 1];
            if (tripValue && tripValue.to && tripValue.from && tripValue.depart) {
                addTrip(tripValue, tripValue.depart, null)
            }
        }

    }

    function returnDate(indx: number) {
        const formValues = getValues()
        if (indx == 0) {
            if (formValues.FareType == 'ON') {
                setValue('FareType', 'RT')
            }
        }
    }

    function addOneDayIndate(currentDate?: Date) {
        const nextDay = new Date(currentDate!);
        nextDay.setDate(nextDay.getDate() + 1);
        return nextDay;
    }

    function swapLocations() {
        const formValue = getValues();
        if (formValue?.trips) {
            const tripValue = formValue.trips[0];

            if (tripValue?.from && tripValue?.to) {
                const tempFrom = { ...tripValue.from };
                setValue('trips.0.from', tripValue.to);
                setValue('trips.0.to', tempFrom);
            }
        }
    }


    function selectAiportOpen(indx: number, dest: string) {
        const formValue = getValues();
        if (isUnder950) {
            setShowMobileAirport(true)
            setSelectAirportindex(indx)
            setSelectAirportFromTo(dest)
            if (formValue.trips) {
                setSelectedAirportIata(dest == 'from' ? formValue.trips[indx]?.from?.iata : formValue.trips[indx]?.to?.iata)
            }
        } else {
            if (formValue.trips) {
                formValue.trips.forEach((x: any, i: number) => {
                    if (i == indx) {
                        if (dest == 'from') {
                            setValue(`trips.${i}.from.airportOpen`, true)
                            setValue(`trips.${i}.to.airportOpen`, false)
                        }
                        if (dest == 'to') {
                            setValue(`trips.${i}.to.airportOpen`, true)
                            setValue(`trips.${i}.from.airportOpen`, false)
                        }
                    } else {
                        setValue(`trips.${i}.to.airportOpen`, false)
                        setValue(`trips.${i}.from.airportOpen`, false)
                    }
                })
            }
        }

    }

    function closeOptionsSidePanel() {
        setShowMobileAirport(false)
        setIsTravelPopUpOpen(false)
    }

    function closeAirport(indx: number) {
        setValue(`trips.${indx}.from.airportOpen`, false)
        setValue(`trips.${indx}.to.airportOpen`, false)
        closeOptionsSidePanel();
    }

    function selectAirport(value: AirportList, index: number, dest: string) {
        const form = tripArrayGet![index]!;
        if (dest == 'from') {
            if (value.code != form.to.iata) {
                setValue(`trips.${index}.from.airportOpen`, false)
                setValue(`trips.${index}.from.city`, value.city_name)
                setValue(`trips.${index}.from.airport`, value.name)
                setValue(`trips.${index}.from.country`, value.country)
                setValue(`trips.${index}.from.iata`, value.code)
                updateSecType(getValues())
            }
        }
        if (dest == 'to') {
            if (value.code != form.from.iata) {
                setValue(`trips.${index}.to.airportOpen`, false)
                setValue(`trips.${index}.to.city`, value.city_name)
                setValue(`trips.${index}.to.airport`, value.name)
                setValue(`trips.${index}.to.country`, value.country)
                setValue(`trips.${index}.to.iata`, value.code)
                updateSecType(getValues())
            }
        }
        closeOptionsSidePanel();
    }

    function updateSecType(value: any) {
        const allCountriesSame = value.trips.every((flight: TripsForm) =>
            flight.from.country.toLowerCase() === flight.to.country.toLowerCase()
        );
        if (allCountriesSame) {
            setValue('SecType', 'D')
        } else {
            setValue('SecType', 'I')
        }
    }

    function scheduleCall() {
        return;
    }

    function searchFlight() {
        localStorage.setItem('dySearchSectorData', JSON.stringify(getValues()));
        sessionStorage.removeItem('dySearchTuiID')
        searchEmit()
    }

    const [departDate, setDepartDate] = useState(new Date());
    const [isPickerOpen, setPickerOpen] = useState<boolean>(false);
    const [isReturnPickerOpen, setIsReturnPickerOpen] = useState<boolean>(false);

    const handleDateChange = (date: any, type: string, tripIndex: number) => {
        if (type === 'O') {
            setValue('trips.0.depart',date)
            setTimeout(() => setPickerOpen(false), 0);
            setDepartDate(date);
            onwardDateClick(date);
        } else {
            setValue('trips.0.return',date)
            setTimeout(() => setIsReturnPickerOpen(false), 0);
            setReturnDateValue(date);
            returnDate(tripIndex);
        }
    };


    function getMinDate(fareType: string, tripIndex: number, depart: any): Date {
        if (fareType !== 'MC') return toDay;
        return tripIndex === 0 ? toDay : depart;
    }


    function onwardDateClick(date: any) {
        if(getValues().FareType === 'RT'){
            const currentReturnDate = getValues(`trips.${0}.return`)
            if(currentReturnDate){
                if(isDateLessThan(currentReturnDate,date)){
                    const nextDate = addOneDayIndate(date)
                    setValue('trips.0.return',nextDate)
                    setReturnDateValue(nextDate)
                    setIsReturnPickerOpen(true)
                    // if(returnDatePickerDivRef &&  returnDatePickerDivRef.current) {
                    //     returnDatePickerDivRef.current.click()
                    // }
        }
    }
        }
    }

    function isDateLessThan(date1: Date | string, date2: Date | string): boolean {
        const d1 = new Date(date1);
        const d2 = new Date(date2);
        return d1 < d2;
    }


    // useEffect(() => {
    //     if(isReturnPickerOpen){
    //         setPickerOpen(false)
    //     }
    // },[isReturnPickerOpen])
    // useEffect(()=>{
    //     if(departDate){
    //         setPickerOpen(false)
    //     }
    // },[departDate])
    const getMinDateRet = (depart: any) => {
        return depart || new Date();
    };

    return (
        <>
            <div className={styles["dy-flight-search-section-div"]}>
                <form>
                    {(() => {
                        if (!isModify) {
                            return <>
                                {/* <div className={styles["service-section-div"]} >
                                    <div className={styles["service-postision"]}>
                                        <div className={`${styles["service-data"]} ${styles["active"]}`}>
                                            <FlightIcon className={styles["mat-icon"]} />
                                            <span>FLIGHTS </span>
                                        </div>
                                        <div className={styles["service-data"]}>
                                            <LanguageIcon className={styles["mat-icon"]} />
                                            <span>AIRPORT LOUNGES</span>
                                        </div>
                                    </div>
                                </div> */}

                                {/* <div className={styles["position-div-route"]}>
                                    <div className={styles["sub-service-div"]}>
                                        <div className={styles["active-data"]}>
                                            <FlightIcon className={styles["mat-icon"]} />
                                            <span>Flights</span>
                                        </div>
                                    </div>
                                    <div className={styles["sub-service-div"]}>
                                        <div className={styles["deactive-data"]}>
                                            <LanguageIcon className={styles["mat-icon"]} />
                                            <span>Airport Lounges</span>
                                        </div>
                                    </div>
                                </div> */}
                            </>
                        }

                    })()}

                    <div className={styles["flight-search-section-div"]}>

                        <div className={styles["fare-type-selection-div"]}>

                            <div className={`${styles['selection-bttn']} ${formValues.FareType === 'ON' ? styles['selected-bttn'] : ''}`} onClick={() => changeSearchType('ON')} >
                                <div className={styles["seclection-chekbox"]}>
                                    <FlightIcon className={styles['mat-icon']} />
                                </div>
                                <div>One-way</div>
                            </div>

                            <div className={`${styles['selection-bttn']} ${formValues.FareType === 'RT' ? styles['selected-bttn'] : ''}`} onClick={() => changeSearchType('RT')} >
                                <div className={styles["seclection-chekbox"]}>
                                    <FlightIcon className={styles['mat-icon']} />
                                </div>
                                <div>Return</div>
                            </div>

                        </div>

                        {
                            fields.map((trip, tripIndex) => {
                                const currentTrips = formValues.trips || [];

                                return <div key={trip.id} className={styles["from-to-depart-return-traveller-div"]}>

                                    <div className={styles["airport-div"]}>
                                        <div className={styles["from-to-airport-div"]}>
                                            <div className={styles["from-airport-div"]} onClick={() => selectAiportOpen(tripIndex, 'from')}>
                                                <label className={styles["label-txt"]}><FlightTakeoffIcon className={styles["mat-icon"]} /> From</label>
                                                <div className={styles["weight-txt-div"]}>
                                                    <span className={styles["sub-wght"]}>{isUnder950?currentTrips[tripIndex]?.from?.iata:currentTrips[tripIndex]?.from?.city}</span>
                                                    <span className={styles["sub-lght"]}>{isUnder950?'':'|'} {isUnder950?'': currentTrips[tripIndex]?.from?.iata}</span>
                                                </div>
                                                <div className={styles["sub-text-div"]}>{isUnder950?currentTrips[tripIndex]?.from?.city:currentTrips[tripIndex]?.from?.airport}</div> 
                                            </div>
                                            {(() => {
                                                if (currentTrips[tripIndex]?.from?.airportOpen) {
                                                    return <DyAirportSelection key={tripIndex} location={'from'} close={() => closeAirport(tripIndex)}
                                                        selectedAirport={($event: any) => selectAirport($event, tripIndex, 'from')}
                                                        iata={currentTrips[tripIndex]?.from?.iata}>
                                                    </DyAirportSelection>
                                                }
                                            })()}
                                        </div>
                                        <div className={styles["swap-airport-div"]}>
                                            <div className={styles["swap-bttn"]} onClick={swapLocations}>
                                                <Loop className={styles["mat-icon"]} />
                                            </div>
                                        </div>
                                        <div className={`${styles["from-to-airport-div"]} ${styles["to-airport-div"]}`}>
                                            <div className={styles["from-airport-div"]} onClick={() => selectAiportOpen(tripIndex, 'to')}>
                                                <label className={styles["label-txt"]}><FlightLandIcon className={styles["mat-icon"]} /> To</label>
                                                <div className={styles["weight-txt-div"]}>
                                                    <span className={styles["sub-wght"]}>{isUnder950?currentTrips[tripIndex]?.to?.iata:currentTrips[tripIndex]?.to?.city}</span>
                                                    <span className={styles["sub-lght"]}>{isUnder950?'':'|'} {isUnder950?'': currentTrips[tripIndex]?.to?.iata}</span>
                                                </div>
                                                <div className={styles["sub-text-div"]}>{isUnder950?currentTrips[tripIndex]?.to?.city:currentTrips[tripIndex]?.to?.airport}</div> 
                                            </div>
                                            {(() => {
                                                if (currentTrips[tripIndex]?.to?.airportOpen) {
                                                    return <DyAirportSelection location={'to'} close={() => closeAirport(tripIndex)}
                                                        selectedAirport={($event: any) => selectAirport($event, tripIndex, 'to')}
                                                        iata={currentTrips[tripIndex]?.to?.iata}>
                                                    </DyAirportSelection>
                                                }
                                            })()}
                                        </div>
                                    </div>

                                    <div className={styles["depart-return-date-div"]}>

                                        <div className={styles["date-data-div"]} onClick={() => setPickerOpen(true)}>
                                            <label className={styles["label-txt"]}><DateRange className={styles["mat-icon"]} /> Depart</label>
                                            <DatePicker
                                                ref={departDatePickerRef}
                                                selected={departDate}
                                                onChange={($e) => handleDateChange($e, 'O', tripIndex)}
                                                minDate={getMinDate(formValues.FareType, tripIndex, formValues.trips[tripIndex - 1]?.depart)}
                                                className={styles["hide-date"]}
                                                open={isPickerOpen}
                                                onClickOutside={() => setPickerOpen(false)}
                                            />
                                            <div className={styles["weight-txt-div"]}>
                                                <span className={styles["sub-wght"]}>{formatDate(departDate, 'd')}</span>
                                                <span className={styles["sub-lght"]}>{formatDate(departDate, "LLL`yy")}</span>
                                            </div>
                                            <div className={styles["sub-text-div"]}>{formatDate(departDate, 'EEEE')}</div>
                                        </div>

                                        <div ref={returnDatePickerDivRef} className={styles["date-data-div"]} onClick={() => setIsReturnPickerOpen(true)}>
                                            <label className={styles["label-txt"]}><DateRange className={styles["mat-icon"]} /> Return</label>
                                            <DatePicker
                                                selected={returnDateValue}
                                                onChange={($e) => handleDateChange($e, 'R', tripIndex)}
                                                minDate={getMinDateRet(departDate)}
                                                className={styles["hide-date"]}
                                                open={isReturnPickerOpen}
                                                onClickOutside={() => setIsReturnPickerOpen(false)}
                                            />
                                            {formValues.FareType === 'ON' && (
                                                <>
                                                    <div className={styles["no-date-found"]}>Book a return to save more</div>
                                                </>
                                            )}
                                            {formValues.FareType === 'RT' && (
                                                <>
                                                    <div className={styles["weight-txt-div"]}>
                                                        <span className={styles["sub-wght"]}>{returnDateValue ? format(returnDateValue, 'd') : ''}</span>
                                                        <span className={styles["sub-lght"]}>{returnDateValue ? format(returnDateValue, "LLL`yy") : ''}</span>
                                                    </div>
                                                    <div className={styles["sub-text-div"]}>{returnDateValue ? format(returnDateValue, 'EEEE') : ''}</div>
                                                </>
                                            )}

                                        </div>

                                        <div className={styles["travel-class-div"]}>
                                            <div className={styles["trveler-data"]} onClick={openTravelerAdd}>
                                                <label className={styles["label-txt"]}><People className={styles["mat-icon"]} /> Travellers & Class</label>
                                                <div className={styles["weight-txt-div"]}>
                                                    <span className={styles["sub-wght"]}>{paxCount}</span>
                                                    <span className={styles["sub-lght"]}>Pax</span>
                                                </div>
                                                <div className={styles["sub-text-div"]}>
                                                    {formValues.cabin == 'E' ? 'Economy' :
                                                        formValues.cabin == 'PE' ? 'Premium Economy' :
                                                            formValues.cabin == 'B' ? 'Business' : ''}
                                                </div>
                                            </div>
                                            {(() => {
                                                 if (isTravelPopUpOpen && !isUnder950) {
                                                    return <DyTravellerClass initialADT={formValues.travellers?.adult || 0}
                                                        initialCHD={formValues.travellers?.child || 0}
                                                        initialINF={formValues.travellers?.infant || 0}
                                                        initalTravellerClass={formValues.cabin || ''}
                                                        isMobile={isUnder950}
                                                        emitForm={($event) => changeTravelClass($event)}></DyTravellerClass>
                                                }
                                            })()}
                                        </div>

                                    </div>

                                    <div className={styles["flight-search-buttons-div"]}>
                                        <button className={styles["dy-primary-bttn"]} type="button" onClick={searchFlight}><SearchIcon /> </button>
                                    </div>

                                </div>

                            })
                        }

                        </div >

                </form >

            </div >

            {(() => {
                if (isUnder950) {
                    return <>
                        <BottomToTopPopup isOpen={showMobileAirport} onClose={closeOptionsSidePanel} type={'search'}>
                            <DyAirportSelection isOpen={showMobileAirport}  location={selectAirportFromTo} close={() => closeAirport(selectAirportindex)}
                                selectedAirport={($event) => selectAirport($event, selectAirportindex, selectAirportFromTo)} isMobile={isUnder950}
                                iata={selectedAirportIata}>
                            </DyAirportSelection>
                        </BottomToTopPopup>
                        <BottomUpPopup isOpen={isTravelPopUpOpen} OnClose={closeOptionsSidePanel} heading="Travellers & Class" zindex={201}>
                            <DyTravellerClass initialADT={getValues().travellers?.adult || 0}
                                initialCHD={getValues().travellers?.child || 0} isMobile={true}
                                initialINF={getValues().travellers?.infant || 0} initalTravellerClass={getValues().cabin || ''}
                                emitForm={changeTravelClass}>
                            </DyTravellerClass>
                        </BottomUpPopup>

                        {/* <DyBottomTopPanel open={showMobileAirport} close={closeOptionsSidePanel} type={'search'}>
                            <DyAirportSelection location="selectAirportFromTo" close={() => closeAirport(selectAirportindex)}
                                selectedAirport={($event) => selectAirport($event, selectAirportindex, selectAirportFromTo)} isMobile={isMobile}
                                iata={selectedAirportIata}>
                            </DyAirportSelection>
                        </DyBottomTopPanel> */}

                        {/* <DyBottomUpPopup show={isTravelPopUpOpen} hide={closeOptionsSidePanel} heading={'Travellers & Class'} zindex={201}>
                            <DyTravellerClass initialADT={getValues().travellers?.adult || 0}
                                initialCHD={getValues().travellers?.child || 0} isMobile={true}
                                initialINF={getValues().travellers?.infant || 0} initalTravellerClass={getValues().cabin || ''}
                                emitForm={changeTravelClass}>
                            </DyTravellerClass>
                        </DyBottomUpPopup> */}
                    </>
                }
            })()}

        </>
    )
}