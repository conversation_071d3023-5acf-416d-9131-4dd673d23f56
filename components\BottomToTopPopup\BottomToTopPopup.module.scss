@import '../../styles/variable.scss';
.bottom-to-top-popup-container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100dvh; /* Full height of the screen */
    display: flex;
    justify-content: center;
    align-items: flex-end;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 300;
    &.visible {
      opacity: 1;
      visibility: visible;
    }
  }
.dy-bootom-up-popup-overlay{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(#000, 0.7);
    z-index: 200;
}

.dy-bootom-up-popup-div{
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: auto;
    background-color: white;
    overflow: hidden;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    border-radius: 10px 10px 0 0;
    z-index: 201;

    &.show{
        transform: translateY(0);
    }
    &.hide {
        transform: translateY(100%); /* Slide out */
      }
    .header-menu-div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 45px;
        border-bottom: 1px solid #d0d0d0;
        .head-text{
            font-size: 18px;
            color: #000;
            font-weight: 500;
        }
        .icons{
            font-size: 12px;
            cursor: pointer;
            transform: scale(.8);
            transition: all .5s ease;
            opacity: .5;
            &:hover {
                transform: scale(1);
                opacity: 1;
            }
        }
    }
    .close-bttn-container{
        position: fixed;
        height: 50px;
        width: 100%;
        background-color: $primary_color;
        top: 0px;
        z-index: 8;
        display: flex;
        align-items: center;
        justify-content: end;
        padding: 25px;
        .close-bttn{
            height: 35px;
            width: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border-radius: 10px;
            border: 1px solid $primary_color;
            color: $button_color;
            font-size: 30px;
            cursor: pointer;
            transition: 0.2s ease-in-out;
            &:hover{
                transform: scale(1.1);
            }
        }
    }

    .body-div{
        width: 100%;
        height: auto;
        overflow: hidden;
        overflow-y: auto;
        position: relative;
    }
}

@media only screen and (max-width: 768px) {
    .bottom-up-bar-div{
        .header-menu-div{
            padding: 0 10px;
        }
    }
}