import { ReactNode, useEffect, useState } from 'react';
import styles from './DyBottomTopPopup.module.scss'

interface BodyDivProps {
    children: ReactNode;
    open: boolean;
    close: VoidFunction;
    heading?: string;
    type:string;
}


const DyBottomTopPanel: React.FC<BodyDivProps> = ({ children, open = true,  heading = '', close,type=''}: BodyDivProps) => {
    const [classToAdd, setClassToAdd] = useState('')



    useEffect(() => {
        if (Mobile(navigator.userAgent)) {
            setClassToAdd('remove-scroll-mobile');
        } else {
            setClassToAdd('remove-scroll');
        }
    }, []); // Runs once on component mount



    useEffect(() => {
        if (open) {
            document.body.classList.add(classToAdd);
        } else if (classToAdd) {
            document.body.classList.remove(classToAdd);
        }
    }, [open, classToAdd]);


    useEffect(() => {
        return () => {
            if (classToAdd) {
                document.body.classList.remove(classToAdd);
            }
        };
    }, [classToAdd]); // Cleanup when component unmounts



    const closePanel = () => {
        if (close) close();
        setTimeout(() => {
            if (classToAdd) {
                document.body.classList.remove(classToAdd);
            }
        }, 50);
    };


    //[@OverlayAnimation]="show?'visible':'hidden'"
    return <>
        {(() => {
            if (open) {
                return <>
                    <div className={styles["overlay"]} onClick={closePanel}></div>

                    <div className={`${styles["wrapper"]} ${open ? 'open' : ''} ${type === 'search' ? 'full-height' : ''}`} >
                        <div className={styles["outer"]}>
                            {(() => {
                                if (type !== 'search') {
                                    return <div className={`${styles["header-section"]} flex flex-row`} >
                                        <div className={`${styles["left"]} flex flex-row justify-start items-center`} >
                                            <span className={styles["fa fa-chevron-left icon ar-rotate"]} onClick={closePanel}></span>
                                            <h5>{heading}</h5>
                                        </div>
                                    </div>
                                }
                            })()}

                            {children}
                        </div>
                    </div>
                </>
            }

        })()}

    </>

}

export default DyBottomTopPanel;


export function Mobile(userAgent: string) {
    var isMobile = {
        Android: function () {
            return userAgent.match(/Android/i);
        },
        BlackBerry: function () {
            return userAgent.match(/BlackBerry/i);
        },
        IOS: function () {
            return userAgent.match(/iPhone|iPad|iPod/i);
        },
        Opera: function () {
            return userAgent.match(/Opera Mini/i);
        },
        Windows: function () {
            return userAgent.match(/IEMobile/i) || userAgent.match(/WPDesktop/i);
        },
        any: function () {
            return (
                isMobile.Android() ||
                isMobile.BlackBerry() ||
                isMobile.IOS() ||
                isMobile.Opera() ||
                isMobile.Windows()
            );
        },
    };

    return isMobile.any();
}
