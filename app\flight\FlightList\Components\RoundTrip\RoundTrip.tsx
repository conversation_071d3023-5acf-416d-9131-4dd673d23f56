import { FlightLand, FlightTakeoff, KeyboardArrowDown, Update } from '@mui/icons-material'
import React, { useEffect, useState } from 'react'
import styles from './RoundTrip.module.scss';
import { Journey, scheduleSave } from 'models/flight-list-response-model';
import { useSyncState } from 'helpers/sync-state';
import { FormSearch, GetExpressSearchBody, TripsForm } from 'models/flight-search-model';
import { useFlightState } from 'services/flight-state-service';
import { scheduleBodySet } from 'adapters/flight-search-adapter';
import FlightApiService from 'services/flight-api-service';
import { any } from 'zod';
import FLightOneWayList from './FlightOneWayList/FLightOneWayList';
import SelectedCardPreview from './SelectedCard/SelectedCardPreview';
import Swal from 'sweetalert2';
import { showNetworkError } from 'components/NetWorkAlert/showNetworkError';
import { Loader } from 'components/loader/loader';
import { FlightDetailsEmitData } from 'models/flight-info-ssr-model';

interface FlightListProps {
    currency: string;
    isMobile: boolean;
    getFlightSearchBody: FormSearch;
    onNavigate: (url: string) => void;
  }
const RoundTrip: React.FC<FlightListProps> = ({getFlightSearchBody,currency = "INR" , isMobile , onNavigate}) => {
    const [isOnwardOpen,setIsOnwardOpen] = useState<boolean>(true)
    const [isReturnOpen,setIsReturnOpen] = useState<boolean>(false)
    const [onWardList,setOnwardList] = useState<Journey[]>([])
    const [returnList,setReturnList] = useState<Journey[]>([])
    const [onWardSelected,setOnwarSelected] = useState<Journey | undefined>();
    const [returnSelected,setReturnSelected] = useState<Journey | undefined>();
    const [isLoading,setIsloading] = useState<boolean>(true)
    const [TUI, setTUI] = useSyncState<string>('');
    const [isCache,setIsCache] = useState<boolean>(true)
    const [tripList,setTripList] = useState<TripsForm>()
    const [showFlightDetails, setShowFlightDetails] = useState<boolean>(false)
    const { flightSchedule , setFlightDetailsBodyData, setIsFlightDetailsPopup} = useFlightState();
    const api = FlightApiService

    useEffect(()=>{
        if(getFlightSearchBody){
            setTripList(getFlightSearchBody.trips[0])
            checkScheduledData()
        }
    },[getFlightSearchBody])

    useEffect(()=>{
        if(isOnwardOpen){
            setIsReturnOpen(false)
        }else if(isReturnOpen){
            setIsOnwardOpen(false)
        }
    },[isOnwardOpen,isReturnOpen])

    function checkScheduledData(){
        setIsOnwardOpen(true);
        setIsReturnOpen(false);
        setOnwardList([]);
        setReturnList([]);
        setOnwarSelected(undefined);
        setReturnSelected(undefined);
        setIsloading(true);
        setIsCache(true)

        const scheduleData: scheduleSave[] = flightSchedule;
        if (scheduleData && scheduleData.length > 0) {
            const searchSector = getFlightSearchBody?.trips.map((trip: any) => trip.from.iata + '-' + trip.to.iata).join('-');
            if (searchSector === scheduleData[0]?.Search) {
              apiCalls();
            }
          } else {
            apiCalls();
          }
    }

    async function apiCalls(){
        const tuiID = localStorage.getItem('dySearchTuiID')
        if(tuiID){
            setTUI(tuiID);
            SearchListApi(tuiID , 0);
        }else{
            setIsCache(true);
            searchApi()
        }
    }

    async function searchApi(){
        const body = scheduleBodySet(getFlightSearchBody, 'RT');
        try{
            const data = await api.callExpressSearch(body)
            if(data.Code === 200){
                if(data.sh_price === true){
                    setIsCache(false)
                }else{
                    setIsCache(true)
                }

                if(data.TUI){
                    setTUI(data.TUI)
                    sessionStorage.setItem('dySearchTuiID',data.TUI)
                    SearchListApi(data.TUI , 0)
                }else{
                    searchApi()
                }
                if(data.Trips && data.Trips[0] && data.Trips[0].Journey){
                    setOnwardList(data.Trips[0].Journey)
                    console.log("onward data",data.Trips[0].Journey);

                }
                if(data.Trips && data.Trips[1] && data.Trips[1].Journey){
                    setReturnList(data.Trips[1].Journey)
                    setIsloading(false)
                }
            }
        }catch (error){
            setIsloading(false);
        }
    }

    async function SearchListApi(tui:string , callCount = 0) {
        const body:GetExpressSearchBody = {
            ClientID: '',
            TUI: tui
        }
        try{
            const data = await api.getExpressSearch(body)
            if(data.Code === 200){
                if(data.Trips && data.Trips[0] && data.Trips[0].Journey){
                    setOnwardList(data.Trips[0].Journey)
                }
                if(data.Trips && data.Trips[1] && data.Trips[1].Journey){
                    setReturnList(data.Trips[1].Journey)
                    setIsloading(false)
                }
            }
            if(!data.Completed){
                if(callCount < 9){
                    setTimeout(() => {
                        SearchListApi(tui , callCount+1);
                    }, 1000);
                }else{
                    showNetworkError(() => onNavigate('/flight/FlightHomePage'))
                }
            }else{
                setIsCache(false)
            }
        }catch(error){
            setIsloading(false)
        }
    }

    function handleSelection(journeyItem:Journey,type:string){
      if(type === 'ONWARD'){
            setOnwarSelected(journeyItem)
            setIsOnwardOpen(false);
            setTimeout(() => (setIsReturnOpen(true)),600)

        }else if(type === 'RETURN'){

        const selectedReturn = journeyItem;
        if(selectedReturn){
            const isContinue = checkHvrDffrent(onWardSelected?.ArrivalTime || '',selectedReturn?.DepartureTime || '')
            if(isContinue){
                setReturnSelected(journeyItem);
                setIsReturnOpen(false);
            }else{
                alert('Please select different time')
            }
        }

      }
    }

    function checkHvrDffrent(date1: string, date2: string): boolean {
        if (date1 === '' && date2 === '') {
            return false;
        }
        const time1 = new Date(date1).getTime();
        const time2 = new Date(date2).getTime();

        const timeDiff = Math.abs(time1 - time2);

        const hoursDiff = timeDiff / (1000 * 3600);

        if (hoursDiff >= 3) {
            return true;
        } else {
            return false
        }
    }

    function BookNow() {
        const body = [{
            "Trips": [
                {
                    "Amount": onWardSelected?.GrossFare,
                    "Index": onWardSelected?.Index,
                    "ChannelCode": onWardSelected?.ChannelCode ? onWardSelected.ChannelCode : '',
                    "OrderID": 1,
                    "TUI": TUI
                },
                {
                    "Amount": returnSelected?.GrossFare,
                    "Index": returnSelected?.Index,
                    "ChannelCode": returnSelected?.ChannelCode ? returnSelected.ChannelCode : '',
                    "OrderID": 2,
                    "TUI": TUI
                }
            ],
            "ClientID": "",
            "Mode": "SS",
            "Options": "A",
            "Source": "SF",
            "TripType": "RT"
        }]
        sessionStorage.removeItem('dygPriceListId');
        sessionStorage.removeItem('dySearchTuiID');
        sessionStorage.setItem('dyPricingBody', JSON.stringify(body));

        // sessionStorage.setItem('dyPricingBody', JSON.stringify(body));
        // route.navigate(['/flight-bookings/payment'])
        onNavigate("/flight/FlightReview")
    }

    function handleShowFlightDetails(){
        const body: FlightDetailsEmitData = {
            TripType: 'ON',
            Trips: [[{
                "Amount": onWardSelected ? onWardSelected.GrossFare : 0,
                "ChannelCode": null,
                "Index": onWardSelected?.Index || '',
                "OrderID": 1,
                "TUI": TUI
            },{
                "Amount": returnSelected ? returnSelected.GrossFare : 0,
                "ChannelCode": null,
                "Index": returnSelected?.Index || '',
                "OrderID": 2,
                "TUI": TUI
            }]
        ]
            }
            setFlightDetailsBodyData(body)
            setIsFlightDetailsPopup(true)
    }



  return (
    <>
        {isLoading ? (
              <Loader />
            ):(
              <></>
            )
            }
        <div className="container mx-auto">
            <div className={`${styles['card-list-container']}`}>
                {/* onward */}
                <div className={`${styles['selected-card-container']}`}>
                    <h2><span>Onward</span>{tripList?.from.city} - {tripList?.to.city}</h2>
                    <div className={`${styles['card-preview-container']} ${onWardSelected && !isOnwardOpen ? styles.open : styles.closed}`}>
                        <SelectedCardPreview
                            handleClick={() => setIsOnwardOpen(!isOnwardOpen)}
                            data={onWardSelected}
                            isOpen={isOnwardOpen}
                            isLoading={isLoading}
                            isMobile={isMobile}
                        />
                    </div>
                    <div className={`${styles['selected-content']} ${isOnwardOpen ? styles.open : ''}`}>
                        <FLightOneWayList
                            isMobile={isMobile}
                            isLoading={isLoading}
                            isCache={isCache}
                            data={onWardList}
                            currency={currency}
                            handleSelection={handleSelection}
                            TUI={TUI}
                            flightSearchData={getFlightSearchBody}
                            type='ONWARD'
                            showFlightDetails={showFlightDetails}
                        />
                    </div>

                {/* return */}
                <h2><span>Return</span>{tripList?.to.city} - {tripList?.from.city}</h2>

                    <div className={`${styles['card-preview-container']} ${returnSelected && !isReturnOpen ? styles.open : styles.closed}`}>
                        <SelectedCardPreview
                            handleClick={() => setIsReturnOpen(!isReturnOpen)}
                            data={returnSelected}
                            isOpen={isReturnOpen}
                            isLoading={isLoading}
                            isMobile={isMobile}
                        />
                    </div>
                    <div className={`${styles['selected-content']} ${isReturnOpen ? styles.open : ''}`}>
                        <FLightOneWayList
                            isMobile={isMobile}
                            isLoading={isLoading}
                            isCache={isCache}
                            data={returnList}
                            currency={currency}
                            handleSelection={handleSelection}
                            TUI={TUI}
                            flightSearchData={getFlightSearchBody}
                            type='RETURN'
                            showFlightDetails={showFlightDetails}
                        />
                    </div>

                {/* total */}
                    <div className={`${styles['total-price-div']} ${returnSelected && onWardSelected ? styles.visible : styles.hidden}`}>
                        <div className={styles["total-container"]}>
                            <div className={styles["total-box"]}>
                              <span className={styles['total-label']}>Total:</span>
                              <span className={styles['total-amount']}>
                                  {onWardSelected?.GrossFare && returnSelected?.GrossFare && (
                                        onWardSelected.GrossFare + returnSelected.GrossFare
                                    )} {currency}
                              </span>
                            </div>
                            <div className={styles["button-group"]}>
                                <button className="linkBtn" onClick={handleShowFlightDetails}>
                                    {showFlightDetails ? 'Hide Details' : 'Show Details'}
                                </button>
                                <button className="dy_primary_bttn" onClick={BookNow}>
                                    Continue
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </>
  )
}

export default RoundTrip