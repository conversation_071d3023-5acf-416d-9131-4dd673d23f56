import styles from './FlightFilterShimmer.module.scss';

export default function FlightFilterShimmer() {
  return (
    <div className={styles['flight-filter-loader-div']}>
      <div className={styles['common-pdng-brdr']}>
          <div className={styles['mat-tab-groups']}>
          </div>
      </div>
      <div className={styles['common-pdng-brdr']}>
        <div className={styles['common-head']} />
        <div className={styles['stops-div']}>
          <div className={styles['stops']} />
          <div className={styles['stops']} />
          <div className={styles['stops']} />
        </div>
        </div>
        <div className={styles['common-pdng-brdr']}>
            <div className={styles['common-head']} />
            <div className={styles['time-card-div']}>
                <div className={styles['time-card']}></div>
                <div className={styles['time-card']}></div>
                <div className={styles['time-card']}></div>
                <div className={styles['time-card']}></div>
            </div>
        </div>
        <div className={styles['common-pdng-brdr']}>
            <div className={styles['common-head']} />
            <div className={styles['time-card-div']}>
                <div className={styles['time-card']}></div>
                <div className={styles['time-card']}></div>
                <div className={styles['time-card']}></div>
                <div className={styles['time-card']}></div>
            </div>
        </div>

      {/* <div className={styles['common-pdng-brdr']}>
        <div className={styles['common-head']} />
        <div className={styles['check-box-div']}>
          <div className={styles['check']} />
          <div className={styles['chec-txt']} />
        </div>
      </div> */}
      {/* <div className={styles['common-pdng-brdr']}>
        <div className={styles['common-head']} />
        <div className={styles['time-list']}>
          <div className={styles['time-div']} />
          <div className={styles['time-div']} />
          <div className={styles['time-div']} />
          <div className={styles['time-div']} />
        </div>
      </div> */}
      <div className={styles['common-pdng-brdr']}>
        <div className={styles['common-head']} />
        <div className={styles['list-check-box']}>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['more-bttn']} />
        </div>
      </div>
      <div className={styles['common-pdng-brdr']}>
        <div className={styles['common-head']} />
        <div className={styles['price-range']} />
        <div className={styles['price-range-txt']}>
          <span className={styles['shine']} />
          <span className={styles['shine']} />
        </div>
      </div>
      <div className={styles['common-pdng-brdr']}>
        <div className={styles['common-head']} />
        <div className={styles['list-check-box']}>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['check-box-div']}>
            <div className={styles['check']} />
            <div className={styles['chec-txt']} />
          </div>
          <div className={styles['more-bttn']} />
        </div>
      </div>
    </div>
  );
}