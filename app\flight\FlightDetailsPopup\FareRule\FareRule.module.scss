@import '../../../../styles/variable.scss';

.flight-details-fare-rule-div {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .head-fare-rule-div {
        font-size: 17px;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .flight-details-fare-rule-card {
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        place-content: stretch flex-start;
        align-items: stretch;
        display: flex;
        flex-direction: column;

        .column-data-div {
            display: flex;
            flex-direction: row;
            place-content: stretch space-between;
            align-items: stretch;
            border-bottom: 1px solid #d0d0d0;

            .label-div {
                display: flex;
                align-items: center;
                width: 40%;
                height: auto;
                padding: 10px;
            }

            .value-txt {
                display: flex;
                align-items: center;
                width: 20%;
                height: auto;
                border-left: 1px solid #d0d0d0;
                padding: 10px;
            }

            h2 {
                font-size: 17px;
                font-weight: 500;
                color: #000;
                margin-bottom: 0;
            }

            h3 {
                font-size: 13px;
                font-weight: 500;
                color: #000;
                margin-bottom: 0;
            }

            p {
                font-size: 12px;
                font-weight: 400;
                color: #000;
                margin-bottom: 0;
            }
        }

        .column-full-data {
            padding: 10px;
            font-size: 14px;
            font-weight: 500;
            color: $text_color;
            background-color: $primary_color;
            border-bottom: 1px solid #d0d0d0;
        }

        .column-full-text-data {
            width: 100%;
            font-size: 12px;
            padding: 10px;
            color: #000;
            border-bottom: 1px solid #d0d0d0;
        }

        .text-rules-div {
            padding: 10px;
            font-size: 12px;

            .text-icon {
                display: flex;
                align-items: start;
                gap: 10px;
                color: #5e5e5e;

                .icon-clr {
                    color: $button_color;
                    svg{
                        font-size: 20px;
                      }
                }
            }
        }
    }
}