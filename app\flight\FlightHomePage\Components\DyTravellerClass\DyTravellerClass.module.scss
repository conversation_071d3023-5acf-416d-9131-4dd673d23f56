@import '../../../../../styles/variable.scss';

.dy_traveller_class_selection_overlay_div {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 9;
}

.dy_traveller_class_selection_div {
    position: absolute;
    left: 0;
    right: 0;
    top: 20px;
    z-index: 99;
    background: #fff;
    border: 1px solid #d4d4d4;
    box-shadow: 0 4px 5px 0 rgba(0, 0, 0, .23);
    min-width: 240px;
    flex-direction: column;
    box-sizing: border-box;
    display: flex;
    place-content: flex-start;
    align-items: flex-start;
    transition: all .5s ease-in .1s;
    @media screen and (max-width: 1170px) {
        right: 0;
        left: auto;
    }
    @media only screen and (max-width: $isMobile) {
        position: relative;
        top: 0;
    }

    .count_div {
        padding: 10px 15px;
        border-bottom: 1px dotted #cbcbcb;
        flex-direction: row;
        box-sizing: border-box;
        display: flex;
        place-content: center space-between;
        align-items: center;
        width: 100%;

        .bttn_grp{
            display: flex;
            align-items: center;
            gap: 10px;
            .add_min_btn{
                width: 30px;
                height: 30px;
                border-radius: 50%;
                color: $button_color;
                border: 2px solid $button_color;
                &:hover{
                    background-color: $button_color;
                    color: #fff;
                }
                &:disabled{
                    background-color: #efefef4d;
                    color: #6b6b6b;
                    border-color: #6b6b6b;
                    cursor: default ;
                }
            }
            .count_tct{
                width: 10px;
                text-align: center;
                font-weight: 600;
            }
        }

        .text_div {
            font-size: 15px;
            color: #000;
            font-weight: 600;

            span {
                display: block;
                font-size: 12px;
                line-height: 12px;
                color: #6b6b6b;
            }
        }

        &:hover {
            background-color: $bg_light;

            .text_div {
                color: $button_color;
            }
        }
    }

    .travel_class_div {
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        .check_data{
            display: flex;
            align-items: center;
            gap: 5px;
            width: 100%;
            border-radius: 5px;
            cursor: pointer;
            .check_box{
                width: 17px;
                height: 17px;
                border-radius: 5px;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid $button_color;
                color: #fff;
                background-color: #fff;
                .checkicon{
                    font-size: 14px;
                }
            }
            .text_div{
                font-weight: 600;
                color: #000;
            }
        }
        .checked{
            color: #fff;
            .text_div{
                color: $button_color;
            }
            .check_box{
                background-color: $button_color;
            }
        }
    }

    .apply_bttn {
        border-top: 1px dotted #cbcbcb;
        padding: 10px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .button-search {
        padding: 10px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        font-size: 15px;
        font-weight: 500;
        line-height: 1;
        transition-duration: 0.4s;
        background: $button_color;
        color: #fff;
        border-radius: 5px;
        outline: 0;
        border: 1px solid $button_color;
        min-width: 100px;
        cursor: pointer;
    }
}