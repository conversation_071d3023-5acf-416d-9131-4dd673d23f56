"use client"

import React, { useEffect, useState } from 'react'
import styles from './FilterFlight.module.scss';
import FlightFilterShimmer from './FlightFilterShimmer/FlightFilterShimmer';
import { FlightSectorFilter } from 'models/flight-list-response-model';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { useSyncState } from 'helpers/sync-state';

interface FilterFlightProps {
    shimmer:boolean,
    currency:string,
    initialFilterData?: FlightSectorFilter[]
    handleFilterChange: (changedData: { key: string; value: any }) => void;
    emitFilter: VoidFunction;
    handleClose ? : VoidFunction
}



const FilterFlight: React.FC<FilterFlightProps> = ({ handleFilterChange, shimmer = true, currency = 'INR', initialFilterData, emitFilter ,handleClose }: FilterFlightProps) => {
    const [isShimmer, setIsShimmer] = useState<boolean>(false);
    const [isAirlinesExpand, setIsAirlinesExpand] = useState<boolean>(false);
    const [isConnectionAirport, setIsConnectionAirport] = useState<boolean>(false);
    const [sliderPosition, setSliderPosition] = useState<number>(0);
    const [isTooltipVisible, setIsTooltipVisible] = useState<boolean>(false)
    const [filterData, setFilterData, filterDataRef] = useSyncState<FlightSectorFilter[]>([])
    const [changePrice, setChangePrice] = useState<boolean>(false)
    const [isMobile,setIsMobile] = useState<boolean>(false)

    useEffect(() => {
        if (initialFilterData) {
            setFilterData(initialFilterData)
        }
    }, [initialFilterData])

    useEffect(() => {
        setIsShimmer(shimmer)
    }, [shimmer])

    const [tabCurrentIndex, setTabCurrentIndex] = useState<number>(0)
    const [departureTime, setDepartureTime] = useState<any[]>([]);
    const [arrivalTime, setArrivalTime] = useState<any[]>([]);
    const [stopSelection, setStopSelection] = useState<any[]>([])
    const [airlineSelection, setAirlineSelection] = useState<any[]>([])
    const [connections, setConnections] = useState<any[]>([])


    useEffect(()=>{
        setIsMobile(window.innerWidth < 950)
    },[])
    // const expandAirline = () => {
    //     setIsAirlinesExpand(!isAirlinesExpand);
    //   };
    // const selectAirlines = (index: number) => {
    //     const fd = filterDataRef.current
    //     // Create a new array with the current state
    //     const updatedAirlines = [...fd[tabCurrentIndex]!.airline];

    //     if (index < 5) {
    //         updatedAirlines[index] = { ...updatedAirlines[index], isSelect: !updatedAirlines[index].isSelect };
    //     } else {
    //         updatedAirlines[index] = { ...updatedAirlines[index], isSelect: !updatedAirlines[index].isSelect };
    //     }

    //     // Update the filterData state with the modified airline array
    //     setFilterData((prevData:any) => {
    //         const newData = [...prevData];
    //         newData[tabCurrentIndex] = {
    //             ...newData[tabCurrentIndex],
    //             airline: updatedAirlines
    //         };
    //         notifyChange("airline", updatedAirlines);
    //         return newData;
    //     });
    // };
    const handlePriceChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        // const newValue = Number(event.target.value); // get the new value from the slider

        // // Update the filterData state with the new price value
        // setFilterData((prevData:any) => {
        //     const newData = [...prevData];
        //     newData[tabCurrentIndex] = {
        //         ...newData[tabCurrentIndex],
        //         priceRange: {
        //             ...newData[tabCurrentIndex].priceRange,
        //             value: newValue // update the value
        //         }
        //     };
        //     notifyChange("priceRange", newValue);
        //     return newData;
        // });
    };
    const handleMouseMove = () => {
        const { Min, Max, value } = filterData[tabCurrentIndex]!.FilterData!.priceRange;
        const percentage = ((Number(value) - Number(Min)) / (Number(Max) - Number(Min))) * 100;
        setSliderPosition(percentage);
    };
    // const handleConnectionAirport = (index:number) => {
    //     const updateConnectionAirport = [...filterData[tabCurrentIndex].connectionAirport];

    //     if (index < 5) {
    //         updateConnectionAirport[index] = { ...updateConnectionAirport[index], isSelect: !updateConnectionAirport[index].isSelect };
    //     } else {
    //         updateConnectionAirport[index] = { ...updateConnectionAirport[index], isSelect: !updateConnectionAirport[index].isSelect };
    //     }

    //     // Update the filterData state with the modified airline array
    //     setFilterData((prevData:any) => {
    //         const newData = [...prevData];
    //         newData[tabCurrentIndex] = {
    //             ...newData[tabCurrentIndex],
    //             connectionAirport: updateConnectionAirport
    //         };
    //         notifyChange("connectionAirport", updateConnectionAirport);
    //         return newData;
    //     });        
    // }
    // const handleStopClick = (value:any) => {
    //     setStopSelection(value)
    //     notifyChange("stops",value)
    // }
    // const handleDepartureTime = (value:any) => {
    //     setDepartureTime(value)
    //     notifyChange("DepTime",value)
    // }
    // const handleArrivalTime = (value:any) => {
    //     setArrivalTime(value)
    //     notifyChange("ArrTime",value)
    // }
    // useEffect(()=>{
    //     setIsTooltipVisible(window.innerWidth < 900)
    // },[])
    // useEffect(() => {

    //     handleMouseMove();
    //   }, [filterData[tabCurrentIndex].priceRange.value, handleMouseMove]);

    //   useEffect(() => {
    //     const timer = setTimeout(() => setIsShimmer(false), 2000);
    //     return () => clearTimeout(timer); 
    //   }, []);

    //   const notifyChange = (key: string, value: any) => {
    //     handleFilterChange({ key, value });
    //   };

    function selectStop(findx: number, i: number) {
        const fd = filterDataRef.current
        if (fd && fd!=undefined && fd[findx]?.FilterData?.stop) {
            const stopItem = fd![findx]!.FilterData!.stop[i]!
            if(stopItem){
                fd[findx]!.FilterData!.stop[i]!.isSelect = !stopItem.isSelect;
                setFilterData(fd)
                if(stopItem.isSelect){
                    setStopSelection(stopSelection.concat([stopItem?.value]))
                }else{
                    setStopSelection(stopSelection.slice(stopSelection.indexOf(stopItem?.value),-1))
                }
                emitFilter()
            }
        }
    }

    function selectDepTime(findx: number, i: number) {
        const fd = filterDataRef.current

        if (fd && fd[findx]?.FilterData?.DepTime ) {
            const depTime = fd[findx]?.FilterData?.DepTime[i];
            if(depTime){
                depTime.isSelect = !depTime.isSelect;
                setFilterData(fd)

                if(depTime.isSelect){
                    setDepartureTime(departureTime.concat([depTime.text]))
                }else{
                    setDepartureTime(departureTime.slice(departureTime.indexOf(depTime.text),-1))
                }
                emitFilter()
            }
        }
    }

    function selectArrTime(findx: number, i: number) {
        const fd = filterDataRef.current

        if (filterData) {
            const arrTime = fd[findx]?.FilterData?.ArrTime[i]
            if(arrTime){
                arrTime.isSelect = !arrTime.isSelect;
                setFilterData(fd)
                if(arrTime.isSelect){
                    setArrivalTime(arrivalTime.concat([arrTime.text]))
                }else{
                    setArrivalTime(arrivalTime.slice(arrivalTime.indexOf(arrTime.text),-1))
                }
                emitFilter()
            }
        }
    }

    function selectAirlines(findx: number, i: number) {
        const fd = filterDataRef.current

        if (filterData) {
            fd[findx]!.FilterData!.Airlines[i]!.isSelect = !fd[findx]!.FilterData!.Airlines[i]!.isSelect;
            setFilterData(fd)
            if(fd[findx]!.FilterData!.Airlines[i]!.isSelect){
                setAirlineSelection(airlineSelection.concat([fd[findx]!.FilterData!.Airlines[i]?.Mac]))
            }else{
                setAirlineSelection(airlineSelection.slice(airlineSelection.indexOf(fd[findx]!.FilterData!.Airlines[i]?.Mac),-1))
            }
            emitFilter()

        }
    }

    function expandAirline(findx: number) {
        const fd = filterDataRef.current

        fd[findx]!.FilterData!.isAirlinesExpand = !fd[findx]!.FilterData!.isAirlinesExpand;
    }

    function selectConnections(findx: number, i: number) {
        const fd = filterDataRef.current
        if (filterData) {
            fd[findx]!.FilterData!.connectionAirport[i]!.isSelect = !fd[findx]!.FilterData!.connectionAirport[i]!.isSelect;
            setFilterData(fd)
            if(fd[findx]!.FilterData!.connectionAirport[i]!.isSelect){
                setConnections(connections.concat([fd[findx]!.FilterData!.connectionAirport[i]?.Airport]))
            }else{
                setConnections(connections.slice(connections.indexOf(fd[findx]!.FilterData!.connectionAirport[i]?.Airport),-1))
            }   
            emitFilter()
        }
    }

    function expandConnection(findx: number) {
        const fd = filterDataRef.current
        fd[findx]!.FilterData!.isconnectionAirportExpand = !fd[findx]!.FilterData!.isconnectionAirportExpand;
    }

    function priceChangeEvent() {
        if (!changePrice) {
            setChangePrice(true)
            setTimeout(() => {
                setChangePrice(false)
                emitFilter()

            }, 1000);
        }
    }
    return (
        <div className={styles['flight-filter-div']}>
            {isShimmer ? (
                <FlightFilterShimmer />
            ) : (
                <div className={styles['flight-filter-content']}>
                    <div className={styles['tabs-container']}>
                        <div className={styles['tabs-div']}>
                            {filterData?.map((item: any, index: number) => {
                                return (
                                    <div onClick={() => setTabCurrentIndex(index)} className={`${styles['tabs-item']} ${tabCurrentIndex === index ? styles.active : ''}`} key={index}>
                                        {item.From} - {item.To}
                                    </div>
                                )
                            })}
                        </div>
                        {/* <div className={styles['tab-content-container']}> */}
                        <div className={styles['filter-flight-show']}>
                            <div className={styles['cpdng']}>
                                <div className={styles['chdng']}>Stops</div>
                                <div className={styles['stops-div']}>
                             
                                    {filterData[tabCurrentIndex]?.FilterData!.stop?.map((item: any, index: number) => {
                                        return (
                                            <div className={`${styles['stops']}  ${item.isSelect  ? styles['stop-active'] : ''}`} key={index} onClick={() => selectStop(tabCurrentIndex, index)}>{item.text}</div>
                                        )
                                    })}
                                </div>
                            </div>
                            <div className={styles['cpdng']}>
                                <div className={styles['chdng']}>DEPARTURE TIME</div>
                                <div className={styles['time-list-div']}>
                                    {filterData[tabCurrentIndex]?.FilterData!.DepTime?.map((item: any, index: number) => {
                                        return (
                                            <div className={`${styles['time-card']} ${item.isSelect ? styles['time-card-active'] : ''}`} key={index} onClick={() => selectDepTime(tabCurrentIndex, index)}>
                                                {/* <span className={`${styles['icons']} material-icons`}>{item?.icon}</span> */}
                                                <AccessTimeIcon />
                                                <span className={styles['txt-time']}>{item?.text}</span>
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>
                            <div className={styles['cpdng']}>
                                <div className={styles['chdng']}>ARRIVAL TIME</div>
                                <div className={styles['time-list-div']}>
                                    {filterData[tabCurrentIndex]?.FilterData!.ArrTime?.map((item: any, index: number) => {
                                        return (
                                            <div className={`${styles['time-card']} ${item.isSelect ? styles['time-card-active'] : ''}`} key={index} onClick={() => selectArrTime(tabCurrentIndex, index)}>
                                                {/* <span className={`${styles.icons} material-icons`}>{item?.icon}</span> */}
                                                <AccessTimeIcon />
                                                <span className={styles['txt-time']}>{item?.text}</span>
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>
                            <div className={styles["cpdng"]}>
                                <div className={styles["chdng"]}>Airlines</div>
                                <div className={styles["check-box-list"]}>
                                    {filterData[tabCurrentIndex]?.FilterData?.Airlines?.slice(0, 5).map((item: any, index: number) => (
                                        <div key={index} className={styles["check-box"]}>
                                            <input
                                                type="checkbox"
                                                checked={item.isSelect}
                                                onChange={() => selectAirlines(tabCurrentIndex, index)}
                                            />
                                            {item.AirlineName}
                                        </div>
                                    ))}
                                </div>

                                {filterData && filterData[tabCurrentIndex]?.FilterData && filterData[tabCurrentIndex]!.FilterData!.Airlines!.length > 5 && (
                                    <div className={styles["linkstyle"]} onClick={() => expandAirline(tabCurrentIndex)}>
                                        {!isAirlinesExpand && `+${filterData[tabCurrentIndex]!.FilterData!.Airlines.length - 5} Airlines`
                                        }
                                    </div>
                                )}

                                {isAirlinesExpand && (
                                    <div className={styles["check-box-list"]}>
                                        {filterData[tabCurrentIndex]?.FilterData!.Airlines.slice(5).map((item: any, index: number) => (
                                            <div key={index} className={styles["check-box"]}>
                                                <input
                                                    type="checkbox"
                                                    checked={item.isSelect}
                                                    onChange={() => selectAirlines(tabCurrentIndex, index + 5)}
                                                />
                                                {item.AirlineName}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                            <div className={styles["cpdng"]}>
                                <div className={styles["chdng"]}>Connecting VIA</div>
                                <div className={styles["check-box-list"]}>
                                    {filterData[tabCurrentIndex]?.FilterData!.connectionAirport?.slice(0, 5).map((item: any, index: number) => (
                                        <div key={index} className={styles["check-box"]}>
                                            <input
                                                type="checkbox"
                                                checked={item.isSelect}
                                                onChange={() => selectConnections(tabCurrentIndex, index)}
                                            />
                                            {item.AirportName}
                                        </div>
                                    ))}
                                </div>

                                {filterData && filterData[tabCurrentIndex]?.FilterData?.connectionAirport && filterData[tabCurrentIndex]!.FilterData!.connectionAirport!.length > 5 && (
                                    <div className={styles["linkstyle"]} onClick={() => setIsConnectionAirport(!isConnectionAirport)}>
                                        {!isConnectionAirport && `+${filterData[tabCurrentIndex]!.FilterData!.Airlines.length - 5} Airport's`}
                                    </div>
                                )}

                                {isConnectionAirport && (
                                    <div className={styles["check-box-list"]}>
                                        {filterData[tabCurrentIndex]?.FilterData!.connectionAirport.slice(5).map((item: any, index: number) => (
                                            <div key={index} className={styles["check-box"]}>
                                                <input
                                                    type="checkbox"
                                                    checked={item.isSelect}
                                                    onChange={() => selectAirlines(tabCurrentIndex, index + 5)}
                                                />
                                                {item.AirportName}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                            <div className={styles['cpdng']}>
                                {filterData && filterData[tabCurrentIndex]?.FilterData && filterData[tabCurrentIndex]!.FilterData!.priceRange?.Max > 0 && filterData[tabCurrentIndex]?.FilterData!.priceRange!.Min! >= 0 && (
                                    <>
                                        <div className={styles['chdng']}>PRICE RANGE</div>
                                        <div className={styles['price-range-div']}>
                                            <div className={styles['slider']}>
                                                <input
                                                    type="range"
                                                    min={filterData[tabCurrentIndex]?.FilterData?.priceRange?.Min}
                                                    max={filterData[tabCurrentIndex]?.FilterData?.priceRange?.Max}
                                                    value={filterData[tabCurrentIndex]?.FilterData?.priceRange?.value}
                                                    onChange={handlePriceChange} // use the new function
                                                    onMouseMove={handleMouseMove}
                                                    onMouseEnter={() => setIsTooltipVisible(true)}
                                                    onMouseLeave={() => setIsTooltipVisible(false)}
                                                />
                                                {isTooltipVisible && (
                                                    <div
                                                        className={styles['tooltip']}
                                                        style={{ left: `${sliderPosition}%`, opacity: sliderPosition ? 1 : 0 }}
                                                    >
                                                        {filterData[tabCurrentIndex]?.FilterData!.priceRange?.value}
                                                    </div>
                                                )}

                                            </div>
                                            <div className={styles['min-max-div']}>
                                                <span>{filterData[tabCurrentIndex]?.FilterData!.priceRange?.Min}</span>
                                                <span>{filterData[tabCurrentIndex]?.FilterData!.priceRange?.Max}</span>
                                            </div>
                                        </div>
                                    </>
                                )}
                            </div>
                            {isMobile && (
                                <div className="button-container">
                                    <button className='dy_primary_bttn' onClick={handleClose}>Applay</button>
                                </div>
                            )}
                        </div>
                        {/* </div> */}
                    </div>
                </div>
            )}
        </div>
    )
}
export default FilterFlight;