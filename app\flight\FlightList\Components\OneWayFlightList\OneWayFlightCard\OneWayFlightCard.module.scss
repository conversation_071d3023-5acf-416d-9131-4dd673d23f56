@import '../../../../../../styles/variable.scss';

.flight-one-way-mob-desk-div {
    .one-way-flight-list-card {
        transition: all .1s ease-in;
        border-radius: 2px;
        border: 1px solid #eeecec9b;
        background-color: #ffff;
        height: 100%;
        flex-direction: column;
        display: flex;
        place-content: stretch flex-start;
        align-items: stretch;
        max-width: 100%;
        gap: 10px;
        box-sizing: border-box;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

        @media only screen and (max-width: 768px) {
            display: none;
        }

        &:hover {
            box-shadow: 0 2px 3px rgba(142, 141, 141, 0.4);
        }
        &.active{
            background-color: whitesmoke;
            border: 2px solid $primary_color;
            box-shadow: 0 2px 3px rgba(142, 141, 141, 0.4);
        }

        .first-div {
            display: flex;
            flex-direction: row;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            padding: 10px;

            .left-div {
                width: 82%;
                display: flex;

                .airline-name-div {
                    flex: 1 1 auto;
                    display: flex;
                    gap: 10px;

                    .logo {
                        width: 35px;
                        height: 35px;
                        border-radius: 50%;
                        overflow: hidden;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .flight-name {
                        font-size: 15px;
                        font-weight: 400;
                    }

                    .flight-no {
                        color: #878686;
                        font-size: 10px;
                        line-height: 10px;
                    }
                }

                .depature-div {
                    align-items: flex-start;
                }

                .arrival-div {
                    align-items: flex-end;
                }

                .date-time-place-div {
                    flex: 1 1 auto;
                    display: flex;
                    flex-direction: column;
                    place-content: flex-start;
                    gap: 3px;

                    .time-div {
                        font-size: 20px;
                        font-weight: 800;
                        line-height: 20px;
                    }

                    .place-div {
                        font-size: 12px;
                        color: #a2a1a1;
                        font-weight: 400;
                        line-height: 14px;
                        text-transform: capitalize;
                    }
                }

            }

            .right-div {
                display: flex;
                flex-direction: column;
                place-content: flex-start;
                align-items: flex-end;
                gap: 5px;

                .icon-btn {
                    font-size: 13px;
                }

                .price-div {
                    font-size: 20px;
                    font-weight: 800;
                    color: $text_color;
                    line-height: 20px;
                }

                a {
                    text-decoration: underline;
                    font-size: 12px;
                }
            }
        }

        .flight-type-div {
            width: 100%;
            overflow: hidden;
            overflow-x: auto;
            display: flex;
            align-items: center;
            gap: 10px;

            .left-right-icon {
                font-size: 16px;
                cursor: pointer;
            }

            .custom-slider-main {
                display: flex;
                overflow: hidden;
                scroll-behavior: smooth;
                gap: 10px;
                padding: 10px;
                background-color: $bg_light;
            }

            .custome-slider-center {
                justify-content: center;
                width: 100%;
            }

            .flight-type-card {
                background: #fff;
                border-radius: 4px;
                cursor: pointer;
                height: auto;
                min-width: max-content;
                padding: 10px;

                .header-type {
                    color: $text_color;
                    font-weight: 600;
                    padding-bottom: 10px;
                    position: relative;
                    border-bottom: 1px solid $bordercolor;
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }

                .price-button-div{
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 0;
                    margin-top: 50px;
                    border-top: 1px solid $bordercolor;
                }

                .price-type {
                    font-weight: 600;
                    color: #000;
                }

                .tp-type-div{
                    font-size: 14px;

                    .section-service{
                        padding: 5px 0;
                    }

                    .head-div{
                        font-size: 14px;
                        font-weight: 700;
                        line-height: 18px;
                        padding-bottom: 5px;
                    }

                    .icon-value{
                        font-size: 12px;
                        font-weight: 400;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                        .icon{
                            font-size: 12px;
                            color: $primary_color;
                        }
                    }
                }

                .body-of-type {
                    display: flex;
                    gap: 30px;
                    font-size: 14px;
                    color: #a2a1a1;
                    padding: 10px 0;

                    .left-type {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                    }

                    .right-type {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                        place-content: flex-end flex-start;
                        align-items: flex-end;
                    }

                    .icon-clr {
                        color: $primary_color;
                    }

                    .icon-txt {
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    }
                }

            }
        }
    }

    .refund-clr {
        color: $success_color;
    }

    .nonrefund-clr {
        color: $error_color;
    }

    .duration-connection-div {
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        text-align: center;

        h3 {
            font-size: 14px;
            font-weight: 700;
            color: #000;
            margin-bottom: 0;

            span {
                color: #a2a1a1;
                font-weight: 400;
            }
        }

        h4 {
            font-size: 12px;
            font-weight: 700;
            color: #000;

            span {
                color: #a2a1a1;
                font-weight: 400;
            }
        }

        .connections-text {
            font-size: 12px;
            font-weight: 400;
            color: #525252;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 100%;
        }

        .flight-line-div {
            width: 100%;
            position: relative;
            height: 8px;
            border-bottom: 2px solid $bg_light;

            &:before,
            &:after {
                width: 7px;
                background: $bg_light;
                height: 7px;
                content: "";
                display: block;
                position: absolute;
                top: 3px;
            }

            &:before {
                left: -5px;
            }

            &:after {
                right: -5px;
            }

            .connections {
                position: absolute;
                left: 0;
                right: 0;
                display: flex;
                align-items: center;
                place-content: center space-evenly;
                gap: 10px;
                top: 4px;

                .connection-dot {
                    width: 7px;
                    height: 7px;
                    border-radius: 50%;
                    background: $text_color;
                    border: 1px solid $text_color;
                    cursor: pointer;
                }
            }
        }
    }

    .one-way-mob-div {
        transition: all .1s ease-in;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ccc;
        background-color: #ffff;
        box-sizing: border-box;
        flex-direction: column;
        place-content: stretch flex-start;
        align-items: stretch;
        max-width: 100%;
        gap: 8px;
        display: none;

        @media only screen and (max-width: 768px) {
            display: flex;
        }

        &:hover {
            box-shadow: 0 3px 9px #0006;
        }

        .airline-name-number-div {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #525252;

            .airline-name {
                font-size: 13px;
                font-weight: 500;
                color: #000;
            }

            .number-div {
                color: #525252;
                font-size: 11px;
                line-height: 11px;
                font-weight: 400;
            }
        }

        .logo-dep-ari-dur-div {
            display: flex;
            width: 100%;
            align-items: center;

            .logo-dep-div {
                flex: 1 1 auto;
                display: flex;
                align-items: center;
                gap: 8px;

                .log-div {
                    width: 28px;
                    height: 28px;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .dep-align-div {
                align-items: flex-start;
            }

            .arr-align-div {
                align-items: flex-end;
                flex: 1 1 auto;
            }

            .dip-ari-div {
                display: flex;
                flex-direction: column;
                place-content: flex-start;
                gap: 2px;

                .time-div {
                    font-size: 17px;
                    line-height: 17px;
                    font-weight: 500;
                    color: #808080;
                }

                .loc-div {
                    font-size: 12px;
                    line-height: 12px;
                    text-transform: capitalize;
                    color: #525252;
                    font-weight: 400;
                }
            }
        }

        .price-div {
            border-top: 1px dashed #ccc;
            padding: 8px 0 0 0;
            justify-content: space-between;
            display: flex;
            align-items: center;

            .price-text {
                font-size: 20px;
                font-weight: 700;
                color: $text_color;
            }

            .refund-txt {
                font-size: 12px;
                font-weight: 700;
                border-radius: 2px;
                padding: 2px 4px;
            }

            .refund-box {
                border: 1px solid $success_color;
                color: $success_color;
            }

            .nonrefund-box {
                border: 1px solid $error_color;
                color: $error_color;
            }
        }

        .flight-details-section {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;

            .details-content {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .detail-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 14px;

                    span {
                        color: #6c757d;
                    }

                    strong {
                        color: #495057;
                        font-weight: 600;
                    }
                }
            }
        }
    }
}

.price-div-shimmer{
    width: 80px;
    height: 20px;
    border-radius: 5px;
}
.button-div-shimmer{
    padding: 10px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    font-size: 15px;
    font-weight: 500;
    line-height: 20px;
    min-width: 100px;
    cursor: not-allowed;
    color: transparent;
}
@keyframes shimmer {
    0% {
      background-position: -100% 0;
    }
    100% {
      background-position: 100% 0;
    }
  }

  .shine {
    animation: shimmer 1.5s infinite linear;
    background: linear-gradient(
      to right,
      #e0e0e0 0%,
      #f7f7f7 50%,
      #e0e0e0 100%
  );
    background-size: 200% 100%;
    border-radius: 4px;
    background-color: #f0f0f0;
  }
@media only screen and (max-width: 768px) {
    .flight-one-way-mob-desk-div {
        .one-way-flight-list-card {
            .first-div {
                display: block;

                .left-div {
                    width: 100%;
                }
            }
        }
    }
}