@keyframes shimmer {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

.one-way-flight-card-shimmer-div{

    .shine {
        background: #f6f7f8;
        animation: shimmer 1.5s infinite linear;
        background: linear-gradient(
            to right,
            #e0e0e0 0%,
            #f7f7f7 50%,
            #e0e0e0 100%
        );
        background-size: 200% 100%;
    }

    .flight-card-shimmer-div{
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ccc;
        background-color: #ffff;

        @media only screen and (max-width: 768px) {
            display: none;
        }

    }
    .flight-card-shimmer-mob-div{
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ccc;
        background-color: #ffff;
        flex-direction: column;
        display: flex;
        place-content: stretch flex-start;
        align-items: stretch;
        max-width: 100%;
        gap: 8px;
        display: none;
        @media only screen and (max-width: 768px) {
            display: block;
        }
    }
    .first-div{
        display: flex;
        flex-direction: row;
        width: 100%;
        align-items: center;
        justify-content: space-between;
    }
    .left-div{
        width: 85%;
        display: flex;
    }
    .airline-flex-auto{
        flex: 1 1 auto;
    }
    .airline-name-div{
        display: flex;
        gap: 10px;
    }
    .logo{
        width: 32px;
        height: 32px;
    }
    .flight-name-no{
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    .flight-name{
        width: 80px;
        height: 15px;
    }
    .flight-no{
        width: 50px;
        height: 13px;
    }
    .time-div{
        height: 20px;
        width: 80px;
    }
    .time-mob-div{
        height: 20px;
        width: 50px;
    }
    .city-div{
        height: 13px;
        width: 65px;
    }
    .date-div{
        width: 100px;
        height: 13px;
    }
    .duration-div{
        display: flex;
        place-content: center flex-start;
        align-items: center;
        flex-direction: column;
        gap: 5px;
    }
    .time-duration{
        width: 100px;
        height: 16px;
    }
    .button-div{
        width: 120px;
        height: 30px;
    }
    .return-flight{
        display: flex;
        flex-direction: column;
        gap: 2px;
        place-content: flex-end flex-start;
        align-items: flex-end;
    }
    .bottom-price-div{
        border-top: 1px dashed #ccc;
        padding-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .refund-box{
        width: 15px;
        height: 15px;
    }
    .flight-line-div{
        width: 100%;
        position: relative;
        height: 8px;
        &::before{
            content: "";
            left: 0;
            width: calc(100% - 10px);
            height: 0;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            border-bottom: 1px dashed #7d7b89;
            z-index: 0;
        }
        &::after{
            content: "\e90a";
            transform: rotate(90deg);
            position: absolute;
            right: -4px;
            top: -6.5px;
            font-family: icomoon!important;
            font-size: 11px;
            color: #c1c1c1;
        }
    }
}

