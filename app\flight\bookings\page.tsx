"use client"

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import FlightApiService from 'services/flight-api-service'
import { BookingList } from 'models/flight-booking-models'
import styles from './page.module.scss'
import BookingListItem from './components/BookingListItem/BookingListItem'

function Page() {
  const router = useRouter()
  const [bookings, setBookings] = useState<BookingList[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBookings()
  }, [])

  const fetchBookings = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const data = await FlightApiService.getBookingList()
      setBookings(data)
    } catch (err) {
      console.error('Error fetching bookings:', err)
      setError('Failed to load bookings. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleViewItinerary = (bookingReference: string) => {
    router.push(`/flight/bookings/itinerary/${bookingReference}`)
  }

  const handleRetry = () => {
    fetchBookings()
  }

  if (isLoading) {
    return (
      <div className={styles.bookingsPage}>
        <div className="container mx-auto">
          <div className={styles.pageHeader}>
            <h1>My Bookings</h1>
            <p>View and manage your flight bookings</p>
          </div>
          <div className={styles.bookingsSkeleton}>
            {/* Desktop Skeleton Header */}
            <div className={styles.skeletonHeader}>
              <div className={`${styles.skeletonHeaderItem} ${styles.wide}`}></div>
              <div className={`${styles.skeletonHeaderItem} ${styles.medium}`}></div>
              <div className={`${styles.skeletonHeaderItem} ${styles.narrow} hideOnTablet`}></div>
              <div className={`${styles.skeletonHeaderItem} ${styles.narrow}`}></div>
              <div className={`${styles.skeletonHeaderItem} ${styles.narrow}`}></div>
            </div>

            {/* Desktop Skeleton Rows */}
            {[...Array(5)].map((_, index) => (
              <div key={index} className={styles.skeletonRow}>
                <div className={`${styles.skeletonItem} ${styles.route}`}></div>
                <div className={`${styles.skeletonItem} ${styles.booking}`}></div>
                <div className={`${styles.skeletonItem} ${styles.passengers} hideOnTablet`}></div>
                <div className={`${styles.skeletonItem} ${styles.amount}`}></div>
                <div className={`${styles.skeletonItem} ${styles.status}`}></div>
              </div>
            ))}

            {/* Mobile Skeleton */}
            <div className={styles.skeletonMobile}>
              {[...Array(3)].map((_, index) => (
                <div key={index} className={styles.skeletonMobileItem}>
                  <div className={styles.skeletonMobileHeader}>
                    <div className={styles.skeletonTitle}></div>
                    <div className={styles.skeletonStatus}></div>
                  </div>
                  <div className={styles.skeletonMobileContent}>
                    {[...Array(4)].map((_, rowIndex) => (
                      <div key={rowIndex} className={styles.skeletonMobileRow}>
                        <div className={styles.skeletonLabel}></div>
                        <div className={styles.skeletonValue}></div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.bookingsPage}>
      <div className="container mx-auto">
        <div className={styles.pageHeader}>
          <h1>My Bookings</h1>
          <p>View and manage your flight bookings</p>
        </div>

        {error ? (
          <div className={styles.errorContainer}>
            <div className={styles.errorMessage}>
              <h3>Oops! Something went wrong</h3>
              <p>{error}</p>
              <button className="dy_primary_bttn" onClick={handleRetry}>
                Try Again
              </button>
            </div>
          </div>
        ) : bookings.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyMessage}>
              <h3>No bookings found</h3>
              <p>You haven't made any flight bookings yet.</p>
              <button
                className="dy_primary_bttn"
                onClick={() => router.push('/flight/FlightHomePage')}
              >
                Book a Flight
              </button>
            </div>
          </div>
        ) : (
          <div className={styles.bookingsList}>
            {/* Desktop/Tablet Header */}
            <div className={styles.listHeader}>
              <div>Flight Details</div>
              <div>Booking Info</div>
              <div className="hideOnTablet">Passengers</div>
              <div>Amount</div>
              <div>Status & Actions</div>
            </div>

            {/* Booking Items */}
            {bookings.map((booking, index) => (
              <BookingListItem
                key={booking.MasterBooking.id || index}
                booking={booking}
                onViewItinerary={() => handleViewItinerary(booking.MasterBooking.booking_reference)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default Page