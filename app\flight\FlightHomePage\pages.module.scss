@import '../../../styles/variable.scss';
.dy-flight-home-div {
  margin: 0 40px;
  padding: 80px 0 20px 0;
  min-height: 50vh;
  background: linear-gradient(rgb(0 0 0 / 39%), rgb(0 0 0 / 51%)), url(https://img.freepik.com/premium-photo/view-airplane-airport-runway-dramatic-sky-generative-ai_192745-1368.jpg) center center;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  border-radius: 20px;
  position: relative;

  .dy-flight-post {
    position: absolute;
    left: 0;
    right: 0;
    bottom: -80px;
  }

  h2 {
    font-weight: 400;
    color: #fff;
    margin-bottom: 50px;
    font-size: 40px;

    @media screen and (max-width: 768px) {
      font-size: 25px;
      font-weight: 500;
      margin-bottom: 30px;
    }
  }
}

.top-deals-div {
  padding-top: 130px;

  h3 {
    font-size: 30px;
    font-weight: 400;
    position: relative;
    width: fit-content;
  }

  .underline-mask {
    /*   background: red; */
    position: relative;

    content: '';
    position: absolute;
    top: 95%;
    width: 150%;
    aspect-ratio: 3 / 1;
    left: 50%;
    transform: translate(-50%, 0);
    border-radius: 50%;
    border: 6px solid $primary_color;
    /* Use a conic gradient mask to hide and show the bits you want */
    --spread: 140deg;
    --start: 290deg;
    mask: conic-gradient(from var(--start), white 0 var(--spread), transparent var(--spread));
  }

  .top-deals-list {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 8px;
    padding: 50px 0;


    img {
      position: absolute;
      object-fit: cover;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      opacity: 0.9;
      transition: opacity .2s ease-out;
    }

    h2 {
      position: absolute;
      inset: auto auto 30px 30px;
      margin: 0;
      transition: inset .3s .3s ease-out;
      font-family: 'Roboto Condensed', sans-serif;
      font-weight: normal;
      text-transform: uppercase;
    }

    p,
    a {
      position: absolute;
      opacity: 0;
      max-width: 80%;
      transition: opacity .3s ease-out;
    }

    p {
      inset: auto auto 80px 30px;
    }

    a {
      inset: auto auto 40px 30px;
      color: inherit;
      text-decoration: none;
    }

    &:hover h2 {
      inset: auto auto 220px 30px;
      transition: inset .3s ease-out;
    }

    &:hover p,
    &:hover a {
      opacity: 1;
      transition: opacity .5s .1s ease-in;
    }

    &:hover img {
      transition: opacity .3s ease-in;
      opacity: 1;
    }

  }

  .material-symbols-outlined {
    vertical-align: middle;
  }

  .div1 {
    grid-column: span 2 / span 2;
    grid-row: span 5 / span 5;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    box-shadow: 0 10px 30px 5px rgba(0, 0, 0, 0.2);
    color: #ffffff;
    height: 36rem;
}

.div2 {
    grid-column: span 3 / span 3;
    grid-row: span 3 / span 3;
    grid-column-start: 3;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    box-shadow: 0 10px 30px 5px rgba(0, 0, 0, 0.2);
    color: #ffffff;
}

.div3 {
    grid-column: span 2 / span 2;
    grid-row: span 2 / span 2;
    grid-column-start: 3;
    grid-row-start: 4;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    box-shadow: 0 10px 30px 5px rgba(0, 0, 0, 0.2);
    color: #ffffff;
}

.div4 {
    grid-row: span 2 / span 2;
    grid-column-start: 5;
    grid-row-start: 4;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    box-shadow: 0 10px 30px 5px rgba(0, 0, 0, 0.2);
    color: #ffffff;
}


}