"use client"
import { ReactNode , useEffect ,useMemo,  useState,   } from "react";
import { GeneralStateContext } from "./GeneralStateContext";
// Define the Props for the Provider
interface GeneralStateProviderProps {
  children: ReactNode;
}

// GeneralStateProvider component
export const GeneralStateProvider: React.FC<GeneralStateProviderProps> = ({ children }) => {
  // Define all states using useState
  const [isUnder950,setIsUnder950] = useState<boolean>(false)
  const [globalRateFlight, setGlobalRateFlight] = useState<number>(1);
  const [globalRate, setGlobalRate] = useState<number>(1);
  const [globalDecimel, setGlobalDecimel] = useState<number>(0);
  const [globalCurrency, setGlobalCurrency] = useState<string>('INR');
  const [globalCurrencySymbol, setGlobalCurrencySymbol] = useState<string>('₹');
  const [countryCode, setCountryCode] = useState<string>('');
  const [globelFlightRate, setGlobelFlightRate] = useState<number>(1);
  const [globelFlightDecimel, setGlobelFlightDecimel] = useState<number>(0);
  const [isUserLoggedIn, setIsUserLoggedIn] = useState<boolean>(false);
  const [userName, setUserName] = useState<string>('');
  const [userMail, setUserMail] = useState<string>('');
  const [userPhoneCountryCode, setUserPhoneCountryCode] = useState<string>('');
  const [userPhonePhoneNumber, setUserPhonePhoneNumber] = useState<string>('');
  const [isMobileSideNavOpened, setIsMobileSideNavOpened] = useState<boolean>(false);
  const [isTearmsRailActive, setIsTearmsRailActive] = useState<boolean>(false);
  const [shortIsoCode, setShortIsoCode] = useState<string>('sa');
  const [invalidEmail, setInvalidEmail] = useState<boolean>(false);
  const [currentRoute, setCurrentRoute] = useState<number>(0);
  const [flightAdultCount, setFlightAdultCount] = useState<number>(1);
  const [flightChildCount, setFlightChildCount] = useState<number>(0);
  const [flightInfantCount, setFlightInfantCount] = useState<number>(0);
  const [userLocation, setUserLocation] = useState<string>('');
  const [maxLimit, setMaxLimit] = useState<number>(0);
  const [childCount, setChildCount] = useState<number>(0);
  const [railAdultCount, setRailAdultCount] = useState<number>(11);
  const [railChildCount, setRailChildCount] = useState<number>(0);
  const [adultCount, setAdultCount] = useState<number>(2);
  const [infantCount, setInfantCount] = useState<number>(0);
  const [toIata, setToIata] = useState<string>('');
  const [fromIata, setFromIata] = useState<string>('');
  const [isOpenLogin, setIsOpenLogin] = useState<boolean>(false);
  const [serviceType, setServiceType] = useState<string>('');
  const [agentMarkup, setAgentMarkup] = useState<number>(0);
  const [markupType, setMarkupType] = useState<string>('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedAgentMarkup = parseInt(sessionStorage.getItem('agentMarkup') || '0', 10);
      const storedMarkupType = sessionStorage.getItem('markuptype') || '';
      setAgentMarkup(storedAgentMarkup);
      setMarkupType(storedMarkupType);


      const handleResize = () => {
        setIsUnder950(window.innerWidth < 950);
      };

      handleResize();
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, []);

  const [tui, setTui] = useState<string[]>([]);
  const [clientId, setClientId] = useState<string>('');
  const [agentGetProfileResponse, setAgentGetProfileResponse] = useState<any>(null);
  const [hotelStatic, setHotelStatic] = useState<any[]>([]);

  // Memoized context value
  const value = useMemo(
    () => ({
      isUnder950,
      setIsUnder950,
      globalRateFlight,
      setGlobalRateFlight,
      globalRate,
      setGlobalRate,
      globalDecimel,
      setGlobalDecimel,
      globalCurrency,
      setGlobalCurrency,
      globalCurrencySymbol,
      setGlobalCurrencySymbol,
      countryCode,
      setCountryCode,
      globelFlightRate,
      setGlobelFlightRate,
      globelFlightDecimel,
      setGlobelFlightDecimel,
      isUserLoggedIn,
      setIsUserLoggedIn,
      userName,
      setUserName,
      userMail,
      setUserMail,
      userPhoneCountryCode,
      setUserPhoneCountryCode,
      userPhonePhoneNumber,
      setUserPhonePhoneNumber,
      isMobileSideNavOpened,
      setIsMobileSideNavOpened,
      isTearmsRailActive,
      setIsTearmsRailActive,
      shortIsoCode,
      setShortIsoCode,
      invalidEmail,
      setInvalidEmail,
      currentRoute,
      setCurrentRoute,
      flightAdultCount,
      setFlightAdultCount,
      flightChildCount,
      setFlightChildCount,
      flightInfantCount,
      setFlightInfantCount,
      userLocation,
      setUserLocation,
      maxLimit,
      setMaxLimit,
      childCount,
      setChildCount,
      railAdultCount,
      setRailAdultCount,
      railChildCount,
      setRailChildCount,
      adultCount,
      setAdultCount,
      infantCount,
      setInfantCount,
      toIata,
      setToIata,
      fromIata,
      setFromIata,
      isOpenLogin,
      setIsOpenLogin,
      serviceType,
      setServiceType,
      agentMarkup,
      setAgentMarkup,
      markupType,
      setMarkupType,
      tui,
      setTui,
      clientId,
      setClientId,
      agentGetProfileResponse,
      setAgentGetProfileResponse,
      hotelStatic,
      setHotelStatic,
      setCurrency: (data: { currency: string }) => setGlobalCurrencySymbol(data.currency),
    }),
    [
      isUnder950,
      globalRateFlight,
      globalRate,
      globalDecimel,
      globalCurrency,
      globalCurrencySymbol,
      countryCode,
      globelFlightRate,
      globelFlightDecimel,
      isUserLoggedIn,
      userName,
      userMail,
      userPhoneCountryCode,
      userPhonePhoneNumber,
      isMobileSideNavOpened,
      isTearmsRailActive,
      shortIsoCode,
      invalidEmail,
      currentRoute,
      flightAdultCount,
      flightChildCount,
      flightInfantCount,
      userLocation,
      maxLimit,
      childCount,
      railAdultCount,
      railChildCount,
      adultCount,
      infantCount,
      toIata,
      fromIata,
      isOpenLogin,
      serviceType,
      agentMarkup,
      markupType,
      tui,
      clientId,
      agentGetProfileResponse,
      hotelStatic,
    ]
  );

  return <GeneralStateContext.Provider value={value}>{children}</GeneralStateContext.Provider>;
};
