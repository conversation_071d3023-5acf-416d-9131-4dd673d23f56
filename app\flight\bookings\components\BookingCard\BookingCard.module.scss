@import '../../../../../styles/variable.scss';

.bookingCard {
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  border-radius: 16px;
  padding: 28px;
  box-shadow: 0 8px 32px rgba(8, 119, 103, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(8, 119, 103, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary_color, lighten($primary_color, 20%));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 20px 60px rgba(8, 119, 103, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);
    border-color: rgba(8, 119, 103, 0.15);

    &::before {
      opacity: 1;
    }
  }

  @media screen and (max-width: 768px) {
    padding: 20px;
    border-radius: 12px;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 18px;
  border-bottom: 1px solid rgba(8, 119, 103, 0.08);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, $primary_color, transparent);
    border-radius: 1px;
  }

  @media screen and (max-width: 480px) {
    flex-direction: column;
    gap: 12px;
  }
}

.bookingInfo {
  h3 {
    font-size: 1.3rem;
    font-weight: 700;
    background: linear-gradient(135deg, $primary_color, darken($primary_color, 10%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 6px 0;
    letter-spacing: -0.01em;
  }
}

.bookingDate {
  font-size: 0.95rem;
  color: #5a6c7d;
  margin: 0;
  font-weight: 500;
  opacity: 0.8;
}

.status {
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &.confirmed {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: 1px solid rgba(40, 167, 69, 0.3);
  }

  &.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    border: 1px solid rgba(255, 193, 7, 0.3);
  }

  &.cancelled {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
    border: 1px solid rgba(220, 53, 69, 0.3);
  }

  &.completed {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
    border: 1px solid rgba(23, 162, 184, 0.3);
  }
}

.flightInfo {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fffe 0%, #f0f9f8 100%);
  border-radius: 12px;
  border: 1px solid rgba(8, 119, 103, 0.06);
  position: relative;

  &::before {
    content: '✈️';
    position: absolute;
    top: 12px;
    right: 12px;
    font-size: 1.2rem;
    opacity: 0.3;
  }
}

.routeInfo {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 20px;

  @media screen and (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
    text-align: center;
  }
}

.departure,
.arrival {
  text-align: center;
  position: relative;

  @media screen and (max-width: 480px) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 12px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
  }
}

.airportCode {
  font-size: 1.6rem;
  font-weight: 800;
  background: linear-gradient(135deg, $primary_color, darken($primary_color, 15%));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 6px;
  letter-spacing: -0.02em;

  @media screen and (max-width: 480px) {
    font-size: 1.3rem;
  }
}

.airportName {
  font-size: 0.9rem;
  color: #5a6c7d;
  margin-bottom: 6px;
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;

  @media screen and (max-width: 480px) {
    max-width: none;
    white-space: normal;
  }
}

.time {
  font-size: 0.95rem;
  font-weight: 700;
  color: #2c3e50;
  background: rgba(8, 119, 103, 0.08);
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.flightPath {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 0 20px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, $primary_color, transparent);
    transform: translateY(-50%);
    opacity: 0.3;
  }

  &::after {
    content: '→';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    color: $primary_color;
    background: white;
    padding: 0 8px;
    font-weight: bold;
  }

  @media screen and (max-width: 480px) {
    padding: 16px 0;
    border-top: 1px solid rgba(8, 119, 103, 0.1);
    border-bottom: 1px solid rgba(8, 119, 103, 0.1);
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    margin: 12px 0;

    &::before,
    &::after {
      display: none;
    }
  }
}

.airline {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.95rem;
  font-weight: 600;
  color: $primary_color;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid rgba(8, 119, 103, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  img {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.duration {
  font-size: 0.85rem;
  color: #5a6c7d;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  padding: 6px 12px;
  border-radius: 16px;
  border: 1px solid rgba(8, 119, 103, 0.1);
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.bookingDetails {
  margin-bottom: 24px;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(8, 119, 103, 0.05);
}

.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  font-size: 0.98rem;
  transition: background-color 0.2s ease;

  &:not(:last-child) {
    border-bottom: 1px solid rgba(8, 119, 103, 0.08);
  }

  &:hover {
    background-color: rgba(8, 119, 103, 0.02);
    border-radius: 6px;
    margin: 0 -8px;
    padding: 10px 8px;
  }

  span:first-child {
    color: #5a6c7d;
    font-weight: 500;
  }

  span:last-child {
    font-weight: 600;
    color: #2c3e50;
  }
}

.amount {
  font-size: 1.2rem !important;
  font-weight: 700 !important;
  background: linear-gradient(135deg, $primary_color, darken($primary_color, 10%)) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.paymentStatus {
  padding: 6px 12px !important;
  border-radius: 20px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &.confirmed {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    border: 1px solid rgba(40, 167, 69, 0.3) !important;
  }

  &.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    color: white !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
  }

  &.cancelled {
    background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
    color: white !important;
    border: 1px solid rgba(220, 53, 69, 0.3) !important;
  }

  &.completed {
    background: linear-gradient(135deg, #17a2b8, #20c997) !important;
    color: white !important;
    border: 1px solid rgba(23, 162, 184, 0.3) !important;
  }
}

.bookingActions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding-top: 8px;

  @media screen and (max-width: 480px) {
    justify-content: center;
  }

  .dy_primary_bttn {
    background: linear-gradient(135deg, $primary_color, darken($primary_color, 10%));
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 0.95rem;
    box-shadow: 0 4px 12px rgba(8, 119, 103, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(8, 119, 103, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }
  }
}
