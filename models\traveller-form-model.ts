export interface TravellerCheckList {
    DOB?: number;
    Nationality?: number;
    PassportNo?: number;
    PLI?: number;
    PDOE?: number;
    PDOI?: number;
    Pancard?: number;
    VisaType?: number;
  }
  
  export interface TravellerFormValues {
    travellers: {
      paxHead: string;
      paxType: string;
      Title: string;
      FName: string;
      LName: string;
    }[];
    phone: string;
    phone_code: string;
    email: string;
  }