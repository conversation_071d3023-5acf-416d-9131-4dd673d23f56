
export interface BookNowBody {
  flight_booking: FlightBooking
  Travellers: Traveller[]
  ContactInfo: ContactInfo
}

export interface FlightBooking {
  provider_info: ProviderInfo
  TUI: string
  ADT: number
  CHD: number
  INF: number
  NetAmount: number
  Trips: Trip[]
  AirlineNetFare: number
  SSRAmount: number
  CrossSellAmount: number
  GrossAmount: number
  Hold: boolean
  ActualHoldTime: number
  ActualDisplayTime: number
}

export interface ProviderInfo {
  code: string
}

export interface Trip {
  Journey: Journey[]
}

export interface Journey {
  Provider: string
  Stops: number
  Segments: Segment[]
  Offer: string
  OrderID: number
  GrossFare: number
  NetFare: number
}

export interface Segment {
  Flight: Flight
  Fares: Fares
}

export interface Flight {
  FUID: string
  VAC: string
  MAC: string
  OAC: string
  Airline: string
  FlightNo: string
  ArrivalTime: string
  DepartureTime: string
  ArrivalCode: string
  DepartureCode: string
  Duration: string
  FareBasisCode: string
  ArrAirportName: string
  DepAirportName: string
  RBD: string
  Cabin: string
  Refundable: string
}

export interface Fares {
  GrossFare: number
  NetFare: number
}

export interface Traveller {
  ID: number
  PaxID: number
  Title: string
  FName: string
  LName: string
  Age: number
  DOB: string
  Gender: string
  PTC: string
  PLI: string
  PDOE: string
  Nationality: string
  PassportNo: string
  VisaType: any
  DocType: string
}

export interface ContactInfo {
  Title: string
  FName: string
  LName: string
  Mobile: string
  Phone: any
  Email: string
  Address: string
  CountryCode: string
  MobileCountryCode: string
  State: string
  City: string
  PIN: any
  GSTAddress: any
  GSTCompanyName: any
  GSTTIN: any
  UpdateProfile: boolean
  IsGuest: boolean
  SaveGST: boolean
  Language: any
}



// list
export interface BookingList {
  MasterBooking: MasterBooking
  FlightBooking: FlightBookingListResponse
}

export interface MasterBooking {
  id: number
  booking_reference: string
  service_type: string
  status: string
  payment_status: string
  user_id: number
  created_at: string
  updated_at: string
  is_deleted: any
}

export interface FlightBookingListResponse {
  id: number
  master_booking_id: string
  provider_info: ProviderInfo
  TUI: string
  Mode: any
  TransactionID: string
  ADT: number
  CHD: number
  INF: number
  NetAmount: number
  AirlineNetFare: number
  SSRAmount: number
  GrossAmount: number
  Hold: boolean
  ActualHoldTime: number
  ActualDisplayTime: number
  Code: any
  Msg: any
  ContactInfo: ContactInfo
  created_at: string
  updated_at: string
  is_deleted: any
  Trips: BookingResponseTrip[]
  Travellers: Traveller[]
}

export interface ProviderInfo {
  code: string
}

export interface ContactInfo {
  id: number
  flight_booking_id: string
  Title: string
  FName: string
  LName: string
  Mobile: string
  Phone: any
  Email: string
  Address: string
  CountryCode: string
  MobileCountryCode: string
  State: string
  City: string
  PIN: any
  GSTAddress: any
  GSTCompanyName: any
  GSTTIN: any
  UpdateProfile: boolean
  IsGuest: boolean
  SaveGST: boolean
  Language: any
}

export interface BookingResponseTrip {
  id: number
  flight_booking_id: string
  Provider: string
  Stops: string
  Offer: string
  OrderID: number
  GrossFare: number
  NetFare: number
  Promo: any
  Segments: BookingResponseSegment[]
}

export interface BookingResponseSegment {
  id: number
  trip_id: string
  Flight: BookingResponseFlight
  Fares: any
}

export interface BookingResponseFlight {
  id: number
  segment_id: string
  FUID: string
  VAC: string
  MAC: string
  OAC: string
  FareBasisCode: string
  Airline: string
  FlightNo: string
  ArrivalTime: string
  DepartureTime: string
  ArrivalCode: string
  DepartureCode: string
  ArrAirportName: string
  DepAirportName: string
  ArrivalTerminal: any
  DepartureTerminal: any
  EquipmentType: any
  RBD: string
  Cabin: string
  Refundable: string
  Duration: string
}

export interface Traveller {
  id: number
  flight_booking_id: string
  PaxID: number
  Title: string
  FName: string
  LName: string
  Age: number
  DOB: string
  Gender: string
  PTC: string
  Nationality: string
  PassportNo: string
  PLI: string
  PDOE: string
  VisaType: any
  DocType: string
}

// New interfaces for detailed booking response
export interface DetailedBookingResponse {
  MasterBooking: DetailedMasterBooking
  FlightBooking: DetailedFlightBooking
}

export interface DetailedMasterBooking {
  id: number
  booking_reference: string
  service_type: string
  status: string
  payment_status: string
  user_id: number
  created_at: string
  updated_at: string
  is_deleted: any
}

export interface DetailedFlightBooking {
  id: number
  master_booking_id: string
  provider_info: ProviderInfo
  TUI: string
  Mode: any
  TransactionID: string
  ADT: number
  CHD: number
  INF: number
  NetAmount: number
  AirlineNetFare: number
  SSRAmount: number
  GrossAmount: number
  Hold: boolean
  ActualHoldTime: number
  ActualDisplayTime: number
  Code: any
  Msg: any
  ContactInfo: DetailedContactInfo
  created_at: string
  updated_at: string
  is_deleted: any
  Trips: DetailedTrip[]
  Travellers: DetailedTraveller[]
}

export interface DetailedContactInfo {
  id: number
  flight_booking_id: string
  Title: string
  FName: string
  LName: string
  Mobile: string
  Phone: any
  Email: string
  Address: string
  CountryCode: string
  MobileCountryCode: string
  State: string
  City: string
  PIN: any
  GSTAddress: any
  GSTCompanyName: any
  GSTTIN: any
  UpdateProfile: boolean
  IsGuest: boolean
  SaveGST: boolean
  Language: any
}

export interface DetailedTrip {
  id: number
  flight_booking_id: string
  Provider: string
  Stops: string
  Offer: string
  OrderID: number
  GrossFare: number
  NetFare: number
  Promo: any
  Segments: DetailedSegment[]
}

export interface DetailedSegment {
  id: number
  trip_id: string
  Flight: DetailedFlight
  Fares: any
}

export interface DetailedFlight {
  id: number
  segment_id: string
  FUID: string
  VAC: string
  MAC: string
  OAC: string
  FareBasisCode: string
  Airline: string
  FlightNo: string
  ArrivalTime: string
  DepartureTime: string
  ArrivalCode: string
  DepartureCode: string
  ArrAirportName: string
  DepAirportName: string
  ArrivalTerminal: any
  DepartureTerminal: any
  EquipmentType: any
  RBD: string
  Cabin: string
  Refundable: string
  Duration: string
}

export interface DetailedTraveller {
  id: number
  flight_booking_id: string
  PaxID: number
  Title: string
  FName: string
  LName: string
  Age: number
  DOB: string
  Gender: string
  PTC: string
  Nationality: string
  PassportNo: string
  PLI: string
  PDOE: string
  VisaType: any
  DocType: string
}
