@import '../../../../../styles/variable.scss';

.flight-filter-div{
    width: 100%;
    border: 1px solid #ccc;
}
.slider {
    position: relative;
    width: auto;

    input {
        width: 100%;
        height: 5px;
        accent-color: $button_color;
    }
    .tooltip {
        position: absolute;
        z-index: 1001;
        transform: translateX(-50%);  // Center-aligns the tooltip
        background-color: $grey;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        white-space: nowrap;
        font-size: 12px;
        top: -30px;
        transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
      }
      
}
.filter-flight-show {
    .cpdng {
        padding: 10px;
        border-bottom: 1px solid #ccc;
    }

    .chdng {
        font-size: 14px;
        font-weight: 700;
        text-transform: uppercase;
        color: #000;
        padding-bottom: 5px;
    }

    .stops-div {
        display: flex;
        gap: 10px;
        width: 100%;

        .stops {
            width: 50px;
            height: 25px;
            border-radius: 4px;
            line-height: 25px;
            text-align: center;
            border: 1px solid $text_color;
            cursor: pointer;
            font-weight: 700;
            font-size: 14px;
            transition: all .5s ease;
            color: $text_color;

            &:hover {
                box-shadow: 0 5px 8px 0 rgba(0, 0, 0, .2);
                transform: translateY(-10%);
            }
        }

        .stop-active {
            background-color: $button_color;
            color: $button_txt_color;
        }
    }

    .check-box-list {
        display: flex;
        flex-direction: column;
    }

    .time-list-div {
        display: flex;
        align-items: center;
        gap: 10px;

        .time-card {
            border: 1px solid $text_color;
            width: calc(25% - 7.5px);
            display: flex;
            place-content: center;
            align-items: center;
            flex-direction: column;
            background-color: #fff;
            transition: all .5s ease;
            padding: 7px 0;
            color: $text_color;
            gap: 5px;
            cursor: pointer;
            border-radius: 4px;

            &:hover {
                box-shadow: 0 5px 8px 0 rgba(0, 0, 0, .2);
                transform: translateY(-10%);
            }

            .icons {
                font-size: 20px;
            }

            .txt-time {
                font-size: 12px;
            }
        }

        .time-card-active {
            background-color: $button_color;
            color: $button_txt_color;
        }
    }

    .price-range-div {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        input{
            width: 100%;
            cursor: pointer;
        }
        .min-max-div {
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #000;
            font-size: 12px;
            font-weight: 500;
        }
    }
    .morebttn {
        width: fit-content;
    }
}
.tabs-container{
    width: 100%;
    padding-bottom: 10px;
    @media screen and (max-width: 950px) {
        max-height: 100dvh;
        overflow-y: auto;
    }
    .tabs-div{
        width: 100%;
        display: flex;
        .tabs-item {
            width: 100%;
            font-weight: bold;
            padding: 12px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            background-color: transparent;
            color: #333;
        
            &:hover {
                background-color: rgba(3, 78, 141, 0.1); 
                color: $blueborder; 
                border-bottom: 2px solid $blueborder;
            }
        
            &.active {
                border-bottom: 2px solid $blueborder;
                background-color: $bg_light; 
                color: $blueborder;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            &.active {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); // Increased shadow on hover and active
            }
        }
    }
}

.check-box-list {
    display: flex;
    flex-direction: column;
    // marg;
  }
  
  .check-box {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    cursor: pointer;
    color: $text_color;
    input[type="checkbox"] {
      appearance: none; 
      width: 20px;
      height: 20px;
      border: 2px solid $button_color; /* Border color */
      border-radius: 4px; /* Rounded corners */
      margin-right: 10px;
      position: relative;
      cursor: pointer;
      &:checked {
        background-color: $button_color; /* Background color when checked */
        border: 2px solid $button_color; /* Maintain border color */
      }
  
      &::after {
        content: '';
        position: absolute;
        width: 6px;
        height: 12px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        left: 6px;
        top: 2px;
        opacity: 0; /* Initially hidden */
      }
  
      &:checked::after {
        opacity: 1; /* Show checkmark when checked */
      }
    }
  
    span {
      font-size: 14px;
      color: $text_color; /* Text color */
      user-select: none; /* Prevent text selection on click */
    }
  }
  
  .linkstyle {
    cursor: pointer;
    color: $blue;
    text-decoration: underline;
    font-size: 12px;
    font-weight: 700;
    color: $button_color;
    &:hover {
      color: $button_color; /* Darker color on hover */
    }
  }
  
//   .tooltip {
//     position: absolute;
//     top: 0px;
//     transform: translateX(-50%);
//     background-color: #333;
//     color: white;
//     padding: 5px 10px;
//     border-radius: 4px;
//     white-space: nowrap;
//     font-size: 12px;
//   }
  