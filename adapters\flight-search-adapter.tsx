import { format } from 'date-fns'; // Optional if you are using date-fns
import { ScheduleBody, TripsForm } from 'models/flight-search-model';


export const formatDate = (date: Date | undefined, formatStr: string): string | null => {
  return date ? format(new Date(date), formatStr) : null;
};

export const scheduleBodySet = (formValue: any, fareType: string): ScheduleBody => {
  const body: ScheduleBody = {
    SecType: formValue && formValue.SecType,
    FareType: fareType,
    ADT: formValue && formValue.travellers.adult,
    CHD: formValue && formValue.travellers.child,
    INF: formValue && formValue.travellers.infant,
    Cabin: formValue && formValue.cabin,
    Source: 'CF',
    Mode: 'AS',
    ClientID: '',
    IsMultipleCarrier: false,
    IsRefundable: false,
    preferedAirlines: null,
    TUI: '',
    YTH: 0,
    Trips: [],
    Parameters: {
      Airlines: '',
      GroupType: '',
      IsDirect: false,
      IsNearbyAirport: true,
      IsStudentFare: false,
      Refundable: '',
    },
  };

  if(formValue ){
  formValue.trips.forEach((x: TripsForm) => {
    if (formValue.FareType === 'RT') {
      body.Trips.push({
        From: x.from.iata,
        To: x.to.iata,
        OnwardDate: formatDate(x.depart, 'yyyy-MM-dd'),
        ReturnDate: formatDate(x.return, 'yyyy-MM-dd'),
        TUI: '',
      });
    } else {
      body.Trips.push({
        From: x.from.iata,
        To: x.to.iata,
        OnwardDate: formatDate(x.depart, 'yyyy-MM-dd'),
        TUI: '',
      });
    }
  });
}

  return body;
};

export const multyCityRequestSet = (formValue: any, fareType: string): ScheduleBody => {
  const body: ScheduleBody = {
    SecType: '',
    FareType: fareType,
    ADT: formValue.travellers.adult,
    CHD: formValue.travellers.child,
    INF: formValue.travellers.infant,
    Cabin: formValue.cabin,
    Source: 'CF',
    Mode: 'AS',
    ClientID: '',
    IsMultipleCarrier: false,
    IsRefundable: false,
    preferedAirlines: null,
    TUI: '',
    YTH: 0,
    Trips: [],
    Parameters: {
      Airlines: '',
      GroupType: '',
      IsDirect: false,
      IsNearbyAirport: true,
      IsStudentFare: false,
      Refundable: '',
    },
    PaymentType: '',
  };

  formValue.trips.forEach((x: TripsForm, i: number) => {
    body.Trips.push({
      From: x.from.iata,
      FromArptName: x.from.airport,
      FromCity: x.from.city,
      OnwardDate: formatDate(x.depart, 'yyyy-MM-dd'),
      OrderId: i + 1,
      ReturnDate: '',
      To: x.to.iata,
      ToArptName: x.to.airport,
      ToCity: x.to.city,
      TUI: '',
    });
  });

  return body;
};
