.shine {
    background: #f6f7f8;
    animation: shimmer 1.5s infinite linear;
    background: linear-gradient(
        to right,
        #e0e0e0 0%,
        #f7f7f7 50%,
        #e0e0e0 100%
    );
    background-size: 200% 100%;
}

@keyframes shimmer {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

.review-flight-card-shimmer-div{
    width: 100%;

    .avilability-tp{
        width: 100%;
        display: flex;
        margin: 0 0 6px 0;
        gap: 6px;
        .left{
            padding: 10px;
            background: #fff;
            border: 1px solid #d0d0d0;
            border-radius: 5px;
            transition: all 0.1s ease-in;
            display: flex;
            width: 94%;
            .spn{
                width: 100%;
                display: flex;
                justify-content: space-between;
                height: 30px;
                .right35 {
                    width: 120px;
                }
                .left35{
                    width: 160px;
                }
            }
              
        }
        .right{
            padding: 10px;
            background: #fff;
            border: 1px solid #d0d0d0;
            border-radius: 5px;
            transition: all 0.1s ease-in;
            width: 6%;
            display: flex;
            align-items: center;
            .bx{
                width: 60px;
                height: 30px;
            }
        }
    }

    .availability-brdr{
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 5px;
        .flight-layout{
            width: 100%;
            height: 45px;
        }
        .avilability{
            width: 100%;
            padding: 10px;
            transition: all 0.1s ease-in;
            display: flex;
            justify-content: space-between;
            .left{
                width: 33%;
                .mid{
                    display: flex;
                    width: 100%;
                    justify-content: center;
                    gap:10px;
                    .first{
                        .stp1{
                            height: 50px;
                            width: 50px;
                        } 
                        .stp2{
                            height: 10px;
                            width: 60px;
                        } 
                        .stp2{
                            height: 10px;
                            width: 60px;
                        }  
                        .stp2{
                            height: 10px;
                            width: 60px;
                        }    
                    }
                    .last{
                        width: 100%;
                        align-items: end;
                        .swp{
                            display: flex;
                            justify-content: end;
                            padding-bottom: 10px;
                            .stp1{
                                height: 20px;
                                width: 60px;
                            }
                        }
                        .stp2{
                            height: 15px;
                            width: 100%;
                        }
                    }
                }
            }
            .mdn{
                width: 33%;
                padding: 10px;
                gap:10px;
                .stp1{
                    height: 20px;
                    width: 160px;
                }
                .stp2{
                    height: 10px;
                    width: 260px;
                }
                .stp3{
                    height: 20px;
                    width: 60px;
                }
                .stp4{
                    height: 10px;
                    width: 60px;
                }
            }
            .last{
                width: 33%;
                justify-content: center;
                gap:10px;
                .bx{
                    width: 100%;
                    height: 80px;
                }
                .btm{
                    height: 20px;
                    width: 100%;
                }
            }
        } 
    }
    
}

@media only screen and (max-width: 768px) {
    .review-flight-card-shimmer-div{
        .avilability-tp{
            border: 1px solid #d0d0d0;
            background: #fff;
            border-radius: 5px;
            .left{
                border: none;
                width: 90%;
                .spn{
                    justify-content: start;
                    gap: 10px;
                    .right35 {
                        width: 60%;
                    }
                    .left35{
                        width: 30%;
                    }
                }
                  
            }
            .right{
                width: 10%;
                padding: 10px 0;
                border: none;
                .bx{
                    width: 25px;
                }
            }
        }
    
        .availability-brdr{
            .flight-layout{
                height: 30px;
            }
            .avilability{
                display: flex;
                flex-direction: column;
                .left{
                    width: 100%;
                    .mid{
                        .first{
                            display: flex;
                            flex-direction: column;
                            gap: 5px;
                            .stp1{
                                height: 30px;
                                width: 30px;
                            }
                        }
                    }
                }
                .mdn{
                    width: 100%;
                    padding: 10px 0;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .stp2{
                        display: none;
                    }
                }
                .last{
                    width: 100%;
                    .btm{
                        display: none;
                    }
                }
            } 
        }
        
    }
}

.review-fare-summary-div-shimmer{
    background: #fff;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    .fare-expance{
        padding: 0 15px;
        border-bottom: 1px solid #ebebeb;
    }
    .fare-head-div{
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .head-text{
            width: 45%;
            height: 20px;
        }
        .head-price-txt{
            width: 30%;
            height: 20px;
        }
    }
    .sub-head-div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 0 10px 20px;
        .sub-txt{
            width: 45%;
            height: 15px;
        }
        .sub-price {
            width: 30%;
            height: 15px;
        }
    }
    .total-div{
        padding: 10px 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .total-text{
            width: 45%;
            height: 25px;
        }
        .total-price{
            width: 35%;
            height: 25px;
        }
    }
}

.review-travel-form-shimmer{
    padding-top: 10px;
    h2{
        font-size: 19px;
        font-weight: 500;
        color: #000;
    }
    .card-form{
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        padding: 10px 15px;
        .pax-travel-div{
            display: flex;
            flex-direction: column;
            gap: 10px;
            .pax-txt{
                width: 200px;
                height: 20px;
            }
            .form-input-div{
                display: flex;
                align-items: center;
                gap: 12px;
                flex-wrap: wrap;
                .form-wdth-20{
                    height: 45px;
                    width: calc(20% - 8px);
                }
                .form-wdth-40{
                    height: 45px;
                    width: calc(40% - 8px);
                }
                .form-wdth-25{
                    height: 45px;
                    width: calc(25% - 9px);
                }
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .review-travel-form-shimmer{
        h2{
            font-size: 17px;
        }
        .card-form{
            padding: 10px;
            .pax-travel-div{
                .pax-txt{
                    width: 150px;
                }
                .form-input-div{
                    gap: 10px;
                    .form-wdth-20{
                        width: 100%;
                    }
                    .form-wdth-40{
                        width: 100%;
                    }
                    .form-wdth-25{
                        width: 100%;
                    }
                }
            }
        }
    }
}

.review-addons-shimmer-div{
    h2{
        font-size: 19px;
        font-weight: 500;
        color: #000;
    }
    .card-div{
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        padding: 15px;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        .addon-card{
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            .addon-round{
                width: 150px;
                height: 80px;
                border-radius: 10px;
            }
            .addon-txt{
                width: 100px;
                height: 20px;
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .review-addons-shimmer-div{
        h2{
            font-size: 17px;
        }
        .card-div{
            padding: 10px;
            gap: 10px;
            .addon-card{
                .addon-round{
                    width: 100px;
                    height: 100px;
                }
                .addon-txt{
                    width: 80px;
                }
            }
        }
    }
}