import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Accordion, AccordionDetails, AccordionSummary, Typography } from '@mui/material';
import React from 'react';
import styles from './ReviewFareSummary.module.scss';

interface SubFare {
  title: string;
  count: number;
  price: number;
  totalPrice: number;
}

interface FareSummary {
  baseFare?: {
    total: number;
    subFare: SubFare[];
  };
  taxs?: {
    total: number;
    subFare: SubFare[];
  };
  addons?: {
    total: number;
    subFare: SubFare[];
  };
  totalAmount?: number;
}

interface FlightReviewFareSummaryProps {
  fareSummary: FareSummary;
  currency: string;
}

const FlightCurrencyConverter = (amount: number, currency: string): string => {
  // Placeholder for currency conversion logic
  return `${currency} ${amount}`;
};

export function FlightReviewFareSummary({ fareSummary }: FlightReviewFareSummaryProps) {
  return (
    <div className={styles["flight-review-fare-summary-div"]}>
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ height: '48px' }}>
          <Typography className={styles["head"]}>Base Fare</Typography>
          <Typography variant="h5">
            {FlightCurrencyConverter(fareSummary?.baseFare?.total || 0, '₹')}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          {fareSummary?.baseFare?.subFare?.map((k, index) => (
            k.totalPrice > 0 && (
              <div className={styles["datas"]} key={index}>
                <p>
                  {k?.title} ({k?.count} X {FlightCurrencyConverter(k?.price, '₹')})
                </p>
                <h6>{FlightCurrencyConverter(k?.totalPrice, '₹')}</h6>
              </div>
            )
          ))}
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ height: '48px' }}>
          <Typography className={styles["head"]}>Tax & Charges</Typography>
          <Typography variant="h5">
            {FlightCurrencyConverter(fareSummary?.taxs?.total || 0, '₹')}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          {fareSummary?.taxs?.subFare?.map((k, index) => (
            k.totalPrice > 0 && (
              <div className={styles["datas"]} key={index}>
                <p>{k?.title}</p>
                <h6>{FlightCurrencyConverter(k?.totalPrice, '₹')}</h6>
              </div>
            )
          ))}
        </AccordionDetails>
      </Accordion>

      {fareSummary?.addons && fareSummary?.addons?.total > 0 && (
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ height: '48px' }}>
            <Typography className={styles["head"]}>Addons</Typography>
            <Typography variant="h5">
              {FlightCurrencyConverter(fareSummary?.addons?.total, '₹')}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            {fareSummary?.addons?.subFare?.map((k, index) => (
              k.totalPrice > 0 && (
                <div className={styles["datas"]} key={index}>
                  <p>{k?.title}</p>
                  <h6>{FlightCurrencyConverter(k?.totalPrice, '₹')}</h6>
                </div>
              )
            ))}
          </AccordionDetails>
        </Accordion>
      )}

      <div className={styles["total-div"]}>
        <h3>Total Amount:</h3>
        <h3>{FlightCurrencyConverter(fareSummary?.totalAmount || 0, '₹')}</h3>
      </div>
    </div>
  );
}

export default FlightReviewFareSummary;