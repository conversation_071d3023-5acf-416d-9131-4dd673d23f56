@import '../../../../../../styles/variable.scss';

.selected-flight-preview-card{
    box-shadow: 0 3px 9px #0006;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #efeffa;
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    cursor: pointer;

    .airline-name{
        font-size: 14px;
        font-weight: 600;
    }
    .price-button-container{
        display: flex;
        gap: 20px;
        .price-div{
            font-size: 18px;
            // text-decoration: underline;
            color: $button_color;
            font-weight: 600;
        }
    }

    .time-duration-div{
        display: flex;
        align-items: center;
        gap: 10px;
        .icon-txt{
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .divider{
            border-bottom: 1px dashed #797979;
            width: 50px;
        }
    }
    @media screen and (max-width: 750px){}
}

.loader-span{
    width: 50px;
    height: 20px;
}