import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { Controller, SubmitHandler, useFieldArray, useForm } from 'react-hook-form';
import { TravellerCheckList } from 'models/traveller-form-model';
import styles from './TravelForm.module.scss';
import { SearchParams } from 'next/dist/server/request/search-params';
import { FormSearch } from 'models/flight-search-model';
import { Loader } from 'components/loader/loader';
import { useRouter } from 'next/navigation';

interface FlightReviewTravellerFormProps {
  handleMakePayment: (travelForm:travel_form) => void;
  travellerCheckList: TravellerCheckList;
  travellerForm: any;
  pax: number[]
}

export interface travel_form{
  email:string
  phone:string
  phone_code:string
  travellers:Passenger[]
}

export interface Passenger {
  DOB: string;
  FName: string;
  LName: string;
  Nationality: string;
  PDOE: string; // Passport Date of Expiry
  PDOI: string; // Passport Date of Issue
  PLI: string;  // Place of Issue (if applicable)
  Pancard: string;
  PassportNo: string;
  Title: string;
  VisaType: string;
  paxHead: string; // Example: "Adult 1"
  paxType: "A" | "C" | "I"; // A = Adult, C = Child, I = Infant
}


const FlightReviewTravellerForm: React.FC<FlightReviewTravellerFormProps> = ({ travellerCheckList, travellerForm , handleMakePayment }) => {

  const [passengerCount,setPassengerCount] = useState<number>(1)
  const {
    register,
    control,
    getValues,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
  } = useForm<travel_form>();

  // // const { control,reset } = useForm<any>({
  // //   defaultValues:travellerForm
  // // });

  const { fields } = useFieldArray({
    control,
    name: 'travellers',
  });

  const [isLoad, setIsLoad] = useState<boolean>(false);
  const router = useRouter();

  useEffect(()=>{
    if (travellerForm) {
      reset(travellerForm); // Reset form with new default values
    }
  },[travellerForm,reset])


  useEffect(() => {
    const searchData = localStorage.getItem('dySearchSectorData')
    if (searchData) {
      const data = JSON.parse(searchData) as FormSearch
      const totalPassengers = data.travellers.adult + data.travellers.child + data.travellers.infant
      setPassengerCount(totalPassengers)

      const defaultPassengers: Passenger[] = []

      // Add Adult passengers
      for (let i = 0; i < data.travellers.adult; i++) {
        defaultPassengers.push({
          DOB: "",
          FName: "",
          LName: "",
          Nationality: "",
          PDOE: "",
          PDOI: "",
          PLI: "",
          Pancard: "",
          PassportNo: "",
          Title: "",
          VisaType: "",
          paxHead: `Adult ${i + 1}`,
          paxType: "A"
        })
      }

      // Add Child passengers
      for (let i = 0; i < data.travellers.child; i++) {
        defaultPassengers.push({
          DOB: "",
          FName: "",
          LName: "",
          Nationality: "",
          PDOE: "",
          PDOI: "",
          PLI: "",
          Pancard: "",
          PassportNo: "",
          Title: "",
          VisaType: "",
          paxHead: `Child ${i + 1}`,
          paxType: "C"
        })
      }

      // Add Infant passengers
      for (let i = 0; i < data.travellers.infant; i++) {
        defaultPassengers.push({
          DOB: "",
          FName: "",
          LName: "",
          Nationality: "",
          PDOE: "",
          PDOI: "",
          PLI: "",
          Pancard: "",
          PassportNo: "",
          Title: "",
          VisaType: "",
          paxHead: `Infant ${i + 1}`,
          paxType: "I"
        })
      }

      reset({
        email: travellerForm?.email || "",
        phone: travellerForm?.phone || "",
        phone_code: travellerForm?.phone_code || "",
        travellers: defaultPassengers
      })
    }
  }, [])

  const handleBooking = () => {
    setIsLoad(true);
    const timer = setTimeout(() => {
      router.push('/flight/FlightSuccess')
      setIsLoad(false);
  }, 3000)
  }

  const onSubmit: SubmitHandler<travel_form> = async (data) => {
    // Validate form data before calling API
    if (!data.email || !data.phone) {
      alert('Please fill in email and phone number');
      return;
    }

    if (!data.travellers || data.travellers.length === 0) {
      alert('Please add traveller information');
      return;
    }

    // Check if all required traveller fields are filled
    for (let i = 0; i < data.travellers.length; i++) {
      const traveller = data.travellers[i];
      if (!traveller || !traveller.Title || !traveller.FName || !traveller.LName) {
        alert(`Please fill in all required fields for ${traveller?.paxHead || `Traveller ${i + 1}`}`);
        return;
      }
    }

    try {
      setIsLoad(true);
      localStorage.setItem('TravelerForm', JSON.stringify(data));

      // Call the parent component's booking function
      await handleMakePayment(data);
    } catch (error) {
      alert('An error occurred while processing your booking. Please try again.');
    } finally {
      setIsLoad(false);
    }
  }
  return (
    <div className={styles["flight-review-travell-form-div"]}>
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Passenger Information Section */}
        <section>
          <h2>Passenger Information</h2>
          <div className={styles["body-div-data"]}>
              <div className={styles["body-padding-div"]}>
                <div className={styles["pax-travel-div"]}>
                  {fields.map((passenger:Passenger,index:number)=>(
                    <div key={index}>
                      <h3>{passenger.paxHead}</h3>
                      <div className={styles["form-input-div"]}>

                      <div className={styles["form-width-20"]}>
                        <div className="input-field">
                          <select
                            className="input-element"
                            id={`travellers.${index}.Title`}
                            {...register(`travellers.${index}.Title`, {
                              required: "Please select a title.",
                            })}
                          >
                            <option value="" disabled selected>
                              Select Title
                            </option>
                            <option value="Mr">Mr</option>
                            <option value="Mrs">Mrs</option>
                            <option value="Ms">Ms</option>
                          </select>
                          <label className="input-label">Title</label>
                        </div>
                      </div>

                        <div className={styles["form-width-40"]}>
                          <div className="input-field">
                          <input
                              className='input-element'
                              id={`travellers.${index}.FName`}
                              type="text"
                              placeholder="Type..."
                              {...register(`travellers.${index}.FName`, {
                                required: "Please provide the first name.",
                              })}
                            />
                            <label className='input-label'>First Name</label>
                          </div>
                        </div>
                        <div className={styles["form-width-40"]}>
                          <div className="input-field">
                          <input
                              className='input-element'
                              id={`travellers.${index}.LName`}
                              type="text"
                              placeholder="Type..."
                              {...register(`travellers.${index}.LName`, {
                                required: "Please provide the last name.",
                              })}
                            />
                            <label className='input-label'>Last Name</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
          </div>
          {/* <div className={styles["body-div-data"]}>
            <div className={styles["body-padng-div"]}>
              <div className={styles["pax-tavel-div"]}>
                {(() => {
                  return fields.map((pax:any, paxId) => {
                    return <div key={paxId}>
                      <h3>{pax.paxHead}</h3>
                      <div className={styles["form-input-div"]}>
                        <div className={styles["form-wdth-20"]}>

                          <FormControl fullWidth variant="outlined">
                            <InputLabel>Title</InputLabel>
                            <Controller
                              name={`travellers.${paxId}.Title`}
                              control={control}
                              render={({ field }) => (
                                <Select {...field} label="Title" {...field}
                                onChange={(event) => {
                                  field.onChange(event.target.value); // Ensures state update
                                }}
                                value={field.value || ''}>
                                  {pax.paxType === "A" && (
                                    <React.Fragment>
                                      <MenuItem value="Mr">Mr</MenuItem>
                                      <MenuItem value="Mrs">Mrs</MenuItem>
                                    </React.Fragment>
                                  )}
                                  {pax.paxType !== "A" && <MenuItem value="Mstr">Mstr</MenuItem>}
                                  <MenuItem value="Ms">Ms</MenuItem>
                                </Select>
                              )}
                            />
                          </FormControl>
                        </div>

                        <div className={styles["form-wdth-40"]}>
                          <FormControl fullWidth variant="outlined">
                            <InputLabel>First Name</InputLabel>
                            <Controller
                              name={`travellers.${paxId}.FName`}
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="First Name"
                                  placeholder="First Name"
                                  variant="outlined"
                                />
                              )}
                            />
                          </FormControl>
                        </div>

                        <div className={styles["form-wdth-40"]}>

                          <FormControl fullWidth variant="outlined">
                            <InputLabel>Last Name</InputLabel>
                            <Controller
                              name={`travellers.${paxId}.LName`}
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="Last Name"
                                  placeholder="Last Name"
                                  variant="outlined"
                                />
                              )}
                            />
                          </FormControl>
                        </div>
                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.DOB === 1) {
                            return <div className="form-wdth-25" >
                              <FormControl fullWidth variant="outlined">
                                <InputLabel shrink>Date of Birth</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.DOB`}
                                  control={control}
                                  render={({ field }) => (
                                    <DatePicker
                                      {...field}
                                      onChange={(date) => field.onChange(date)}
                                      // inputFormat="MM/dd/yyyy"
                                      // renderInput={(params) => (
                                      //   <div style={{ display: 'flex', alignItems: 'center' }}>
                                      //     <input
                                      //       {...params.inputProps}
                                      //       placeholder="Date of Birth"
                                      //       className="date-input-non"
                                      //       readOnly
                                      //     />
                                      //     <InputAdornment position="end">
                                      //       <IconButton edge="end">
                                      //         <CalendarTodayIcon />
                                      //       </IconButton>
                                      //     </InputAdornment>
                                      //   </div>
                                      // )}
                                    />
                                  )}
                                />
                              </FormControl>
                            </div>
                          }
                        })()} */}

                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.Nationality === 1) {
                            return <div className="form-wdth-25"  >
                              <FormControl fullWidth variant="outlined">
                                <InputLabel>Nationality</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.Nationality`}
                                  control={control}
                                  render={({ field }) => (
                                    <Select {...field} label="Nationality">

                                      <MenuItem value="India">India</MenuItem>
                                    </Select>
                                  )}
                                />
                              </FormControl>

                            </div>
                          }
                        })()} */}

                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.PassportNo === 1) {
                            return <div className="form-wdth-25" >
                              <FormControl fullWidth variant="outlined">
                                <InputLabel>Passport Number</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.PassportNo`}
                                  control={control}
                                  render={({ field }) => (
                                    <TextField
                                      {...field}
                                      label="Passport Number"
                                      placeholder="Passport Number"
                                      variant="outlined"
                                    />
                                  )}
                                />
                              </FormControl>

                            </div>
                          }
                        })()} */}

                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.PLI === 1) {
                            return <div className="form-wdth-25" >
                              <FormControl fullWidth variant="outlined">
                                <InputLabel>Issuing Country</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.PLI`}
                                  control={control}
                                  render={({ field }) => (
                                    <Select {...field} label="Issuing Country">

                                      <MenuItem value="India">India</MenuItem>
                                    </Select>
                                  )}
                                />
                              </FormControl>

                            </div>
                          }
                        })()} */}
                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.PDOE === 1) {
                            return <div className="form-wdth-25">
                              <FormControl fullWidth variant="outlined">
                                <InputLabel shrink>Passport Expiry</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.PDOE`}
                                  control={control}
                                  render={({ field }) => (
                                    <DatePicker
                                      {...field}
                                      onChange={(date) => field.onChange(date)}
                                      // inputFormat="MM/dd/yyyy"
                                      // renderInput={(params) => (
                                      //   <div style={{ display: 'flex', alignItems: 'center' }}>
                                      //     <input
                                      //       {...params.inputProps}
                                      //       placeholder="Passport Expiry"
                                      //       className="date-input-non"
                                      //       readOnly
                                      //     />
                                      //     <InputAdornment position="end">
                                      //       <IconButton edge="end">
                                      //         <CalendarTodayIcon />
                                      //       </IconButton>
                                      //     </InputAdornment>
                                      //   </div>
                                      // )}
                                    />
                                  )}
                                />
                              </FormControl>

                            </div>
                          }
                        })()} */}

                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.PDOI === 1) {
                            return <div className="form-wdth-25"  >
                              <FormControl fullWidth variant="outlined">
                                <InputLabel shrink>Passport Issued</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.PDOI`}
                                  control={control}
                                  render={({ field }) => (
                                    <DatePicker
                                      {...field}
                                      onChange={(date) => field.onChange(date)}
                                      // inputFormat="MM/dd/yyyy"
                                      // renderInput={(params:any) => (
                                      //   <div style={{ display: 'flex', alignItems: 'center' }}>
                                      //     <input
                                      //       {...params.inputProps}
                                      //       placeholder="Passport Issued"
                                      //       className="date-input-non"
                                      //       readOnly
                                      //     />
                                      //     <InputAdornment position="end">
                                      //       <IconButton edge="end">
                                      //         <CalendarTodayIcon />
                                      //       </IconButton>
                                      //     </InputAdornment>
                                      //   </div>
                                      // )}
                                    />
                                  )}
                                />
                              </FormControl>
                            </div >
                          }
                        })()} */}

                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.Pancard === 1) {
                            return <div className="form-wdth-25" >
                              <FormControl fullWidth variant="outlined">
                                <InputLabel>Pancard Number</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.Pancard`}
                                  control={control}
                                  render={({ field }) => (
                                    <Select {...field} label="Pancard Number">

                                      <MenuItem value="India">India</MenuItem>
                                    </Select>
                                  )}
                                />
                              </FormControl>
                            </div >
                          }
                        })()} */}

                        {/* {(() => {
                          if (travellerCheckList && travellerCheckList.VisaType === 1) {
                            return <div className="form-wdth-25"  >
                              <FormControl fullWidth variant="outlined">
                                <InputLabel>Visa Type</InputLabel>
                                <Controller
                                  name={`travellers.${paxId}.VisaType`}
                                  control={control}
                                  render={({ field }) => (
                                    <Select {...field} label="VisaType">

                                      <MenuItem value="STUDENT VISA">STUDENT VISA</MenuItem>
                                      <MenuItem value="IMMIGRANT VISA">IMMIGRANT VISA</MenuItem>
                                      <MenuItem value="PERMANENT RESIDENT VISA">PERMANENT RESIDENT VISA</MenuItem>
                                      <MenuItem value="EMPLOYMENT / WORK VISA">EMPLOYMENT / WORK VISA</MenuItem>
                                      <MenuItem value="JOINING FAMILY VISA">JOINING FAMILY VISA</MenuItem>
                                      <MenuItem value="BUSINESS VISA">BUSINESS VISA</MenuItem>
                                      <MenuItem value="TOURIST / VISIT VISA">TOURIST / VISIT VISA</MenuItem>
                                      <MenuItem value="HAJJ VISA">HAJJ VISA</MenuItem>
                                      <MenuItem value="UM RAH VISA">UM RAH VISA</MenuItem>
                                      <MenuItem value="OTHERS (SPECIFY)">OTHERS (SPECIFY)</MenuItem>
                                    </Select>
                                  )}
                                />
                              </FormControl>

                            </div>
                          }
                        })()}


                      </div >
                    </div >
                  })

                })()}

              </div >
            </div >
          </div > */}
        </section >

        {/* Contact Details Section */}
        < section >
          <h2>Contact Details</h2>
          <div className={styles["body-div-data"]}>
            <div className={styles["body-padding-div"]}>
              <div className={styles["pax-travel-div"]}>
                  <div className={styles["form-input-div"]}>
                      <div className={styles["form-width-50"]}>
                        <div className="input-field">
                        <input
                            className='input-element'
                            id={`email`}
                            type="text"
                            placeholder="Type..."
                            {...register(`email`, {
                              required: "Please Enter Your Email.",
                            })}
                          />
                          <label className='input-label'>Email</label>
                        </div>
                      </div>
                      <div className={styles["form-width-50"]}>
                        <div className="input-field">
                        <input
                            className='input-element'
                            id={`phone`}
                            type="text"
                            placeholder="Type..."
                            {...register(`phone`, {
                              required: "Please Enter Your phone.",
                            })}
                          />
                          <label className='input-label'>Phone</label>
                        </div>
                      </div>
                  </div>
              </div>
            </div>
          </div>
        </section >

        <section>
              <div className={styles["payment-bttn-div"]}>
                <button className="dy_primary_bttn" type='submit'>
                  Make Payment
                </button>
              </div>
            </section>
      </form >

      {isLoad && (
            <Loader />
      )}
    </div >
  );
};

export default FlightReviewTravellerForm;