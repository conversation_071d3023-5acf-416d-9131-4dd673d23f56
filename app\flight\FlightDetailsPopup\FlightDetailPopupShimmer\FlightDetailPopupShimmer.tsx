"use client";

import { InfoOutlined, InfoTwoTone } from '@mui/icons-material';
import styles from './FlightDetailPopupShimmer.module.scss';
import React from 'react';

interface ShimmerComponentProps {
  type: 'fareinfo' | 'faresummary' | 'farerule' | 'baggage';
}

export function FlightDetailsPopupShimmer({ type }: ShimmerComponentProps) {
  if (type === 'fareinfo') {
    return (
      <div className={styles["flight-details-info-div"]}>
          {[...Array(2)].map((_, i) => (
            <React.Fragment key={i}>
              <div className={`${styles["trip-text"]} ${styles["shine"]}`}></div>
              <div className={styles["flight-card-div"]}>
                <div className={styles["top-div"]}>
                  <div className={styles["image-name-number"]}>
                    <div className={`${styles["image-div"]} ${styles["shine"]}`}></div>
                    <div className={styles["name-number-div"]}>
                      <div className={`${styles["name-div"]} ${styles["shine"]}`}></div>
                      <div className={`${styles["number-div"]} ${styles["shine"]}`}></div>
                    </div>
                  </div>
                  <div className={styles["aircraft-travel-class-div"]}>
                    {[...Array(2)].map((_, j) => (
                      <div className={styles["label-text-div"]} key={j}>
                        <div className={`${styles["label-txt"]} ${styles["shine"]}`}></div>
                        <div className={`${styles["txt-div"]} ${styles["shine"]}`}></div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className={styles["body-div"]}>
                  <div className={styles["dep-arri-div"]}>
                    <h3 className={styles["shine"]}></h3>
                    {[...Array(3)].map((_, k) => (
                      <div className={`${styles["place-div"]} ${styles["shine"]}`} key={k}></div>
                    ))}
                  </div>
                  <div className={styles["duration-div"]}>
                    <h3 className={styles["shine"]}></h3>
                    <div className={styles["flight-line-div"]}></div>
                  </div>
                  <div className={`${styles["dep-arri-div"]} ${styles["arr-div"]}`}>
                    <h3 className={styles["shine"]}></h3>
                    {[...Array(3)].map((_, k) => (
                      <div className={`${styles["place-div"]} ${styles["shine"]}`} key={k}></div>
                    ))}
                  </div>
                </div>
              </div>
            </React.Fragment>
          ))}
      </div>
    );
  }

  if (type === 'faresummary') {
    return (
      <div className={styles["review-fare-summary-div-shimmer"]}>
        <div className={`${styles["head-div-fare-summary"]} ${styles["shine"]}`}></div>
        <div className={styles["review-fare-summary-card"]}>
          {[...Array(2)].map((_, i) => (
            <div className={styles["fare-expance"]} key={i}>
              <div className={styles["fare-head-div"]}>
                <div className={`${styles["head-text"]} ${styles["shine"]}`}></div>
                <div className={`${styles["head-price-txt"]} ${styles["shine"]}`}></div>
              </div>
              {[...Array(3)].map((_, j) => (
                <div className={styles["sub-head-div"]} key={j}>
                  <div className={`${styles["sub-txt"]} ${styles["shine"]}`}></div>
                  <div className={`${styles["sub-price"]} ${styles["shine"]}`}></div>
                </div>
              ))}
            </div>
          ))}
          <div className={styles["total-div"]}>
            <div className={`${styles["total-text"]} ${styles["shine"]}`}></div>
            <div className={`${styles["total-price"]} ${styles["shine"]}`}></div>
          </div>
        </div>
      </div>
    );
  }

  if (type === 'farerule') {
    return (
      <div className={styles["fare-details-fare-rule-div"]}>
        <div className={`${styles["head-div-fare-rule"]} ${styles["shine"]}`}></div>
        <div className={styles["fare-details-fare-rule-card"]}>
          {[...Array(10)].map((_, i) => (
            <div className={styles["column-datas"]} key={i}>
              <div className={`${styles["data-txt"]} ${styles["shine"]}`}></div>
            </div>
          ))}
          <div className={styles["text-rules-div"]}>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>
                The above data is indicative; fare rules are subject to change by the airline, depending on the fare class,
                and change/cancellation fees may vary based on fluctuations in currency conversion rates.
              </span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>Although we will try to keep this section updated regularly.</span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>Feel free to call our Contact Centre for exact cancellation/change fee.</span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>Cancellation/Date change requests will be accepted 30 hrs prior to departure.</span>
            </div>
            <div className={styles["text-icon"]}>
              <span className={styles["icon-clr"]}><InfoTwoTone/></span>
              <span>GST charges applicable on cancellation penalty.</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (type === 'baggage') {
    return (
      <div className={styles["baggage-inclusion-shimmer-div"]}>
        <div style={{backgroundColor:'#f0eded'}} className={styles['bagge-column-div']}>
            <div className={`${styles["value-data"]} ${styles["table-head-clmn"]}`} >
              <h3>Sector/Flight</h3>
            </div>
            <div className={`${styles["value-data"]} ${styles["table-head-clmn"]}`} >
              <h3>Check in Baggage</h3>
            </div>            
            <div className={`${styles["value-data"]} ${styles["table-head-clmn"]}`} >
              <h3>Cabin Baggage</h3>
            </div>
        </div>
        {[...Array(3)].map((_, i) => (
          <div className={styles["bagge-column-div"]} key={i}>
            {[...Array(3)].map((_, j) => (
              <div className={`${styles["value-data"]}`} key={j}>
                <div className={`${styles["text-value"]} ${styles["shine"]}`}></div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  }

  return null;
}
