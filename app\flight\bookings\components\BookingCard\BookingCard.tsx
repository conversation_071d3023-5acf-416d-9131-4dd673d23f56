import React from 'react'
import { BookingList } from 'models/flight-booking-models'
import styles from './BookingCard.module.scss'

interface BookingCardProps {
  booking: BookingList
  onViewItinerary: () => void
}

const BookingCard: React.FC<BookingCardProps> = ({ booking, onViewItinerary }) => {
  const { MasterBooking, FlightBooking } = booking

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return styles.confirmed
      case 'pending':
        return styles.pending
      case 'cancelled':
        return styles.cancelled
      case 'completed':
        return styles.completed
      default:
        return styles.pending
    }
  }

  const getFirstTrip = () => {
    return FlightBooking.Trips?.[0]
  }

  const getFirstSegment = () => {
    const firstTrip = getFirstTrip()
    return firstTrip?.Segments?.[0]
  }

  const getFirstFlight = () => {
    const firstSegment = getFirstSegment()
    return firstSegment?.Flight
  }

  const firstFlight = getFirstFlight()

  return (
    <div className={styles.bookingCard}>
      <div className={styles.bookingHeader}>
        <div className={styles.bookingInfo}>
          <h3>Booking #{MasterBooking.booking_reference}</h3>
          <p className={styles.bookingDate}>
            Booked on {formatDate(MasterBooking.created_at)}
          </p>
        </div>
        <span className={`${styles.status} ${getStatusClass(MasterBooking.status)}`}>
          {MasterBooking.status}
        </span>
      </div>

      {firstFlight && (
        <div className={styles.flightInfo}>
          <div className={styles.routeInfo}>
            <div className={styles.departure}>
              <div className={styles.airportCode}>{firstFlight.DepartureCode}</div>
              <div className={styles.airportName}>{firstFlight.DepAirportName}</div>
              <div className={styles.time}>{formatTime(firstFlight.DepartureTime)}</div>
            </div>

            <div className={styles.flightPath}>
              <div className={styles.airline}>
                <img
                  src={`/assets/images/AirlineLogo/${firstFlight.MAC}.png`}
                  alt={firstFlight.Airline}
                  width={30}
                  height={30}
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none'
                  }}
                />
                <span>{firstFlight.Airline} {firstFlight.FlightNo}</span>
              </div>
              <div className={styles.duration}>{firstFlight.Duration}</div>
            </div>

            <div className={styles.arrival}>
              <div className={styles.airportCode}>{firstFlight.ArrivalCode}</div>
              <div className={styles.airportName}>{firstFlight.ArrAirportName}</div>
              <div className={styles.time}>{formatTime(firstFlight.ArrivalTime)}</div>
            </div>
          </div>
        </div>
      )}

      <div className={styles.bookingDetails}>
        <div className={styles.detailRow}>
          <span>Passengers:</span>
          <span>{FlightBooking.ADT + FlightBooking.CHD + FlightBooking.INF}</span>
        </div>
        <div className={styles.detailRow}>
          <span>Total Amount:</span>
          <span className={styles.amount}>₹{FlightBooking.GrossAmount?.toLocaleString()}</span>
        </div>
        <div className={styles.detailRow}>
          <span>Payment Status:</span>
          <span className={`${styles.paymentStatus} ${getStatusClass(MasterBooking.payment_status)}`}>
            {MasterBooking.payment_status}
          </span>
        </div>
      </div>

      <div className={styles.bookingActions}>
        <button className="dy_primary_bttn" onClick={onViewItinerary}>
          View Itinerary
        </button>
      </div>
    </div>
  )
}

export default BookingCard
