import { FlightFilterData, Journey } from "models/flight-list-response-model";


export class FlightFilterHelper {

    constructor(private datePipe: Date) { }

    setFlightFilterDate(list: Journey[]) {
        let filter: FlightFilterData = {
            stop: [
                {
                    value: 0,
                    text: '0',
                    isSelect: false
                },
                {
                    value: 1,
                    text: '1',
                    isSelect: false
                },
                {
                    value: 2,
                    text: '1+',
                    isSelect: false
                }
            ],
            Refund: {
                isSelect: false,
                text: 'Refundable'
            },
            DepTime: [
                {
                    min: 5,
                    max: 12,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '05-12'
                },
                {
                    min: 12,
                    max: 18,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '12-18'
                },
                {
                    min: 18,
                    max: 24,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '18-24'
                },
                {
                    min: 24,
                    max: 5,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '24-05'
                }
            ],
            ArrTime: [
                {
                    min: 5,
                    max: 12,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '05-12'
                },
                {
                    min: 12,
                    max: 18,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '12-18'
                },
                {
                    min: 18,
                    max: 24,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '18-24'
                },
                {
                    min: 24,
                    max: 5,
                    isSelect: false,
                    icon: 'fa fa-clock',
                    text: '24-05'
                }
            ],
            Airlines: [],
            isAirlinesExpand: false,
            priceRange: {
                Min: list![0]!.GrossFare ? list![0]!.GrossFare : 0,
                Max: list![0]!.GrossFare ? list![0]!.GrossFare : 0,
                value: list![0]!.GrossFare ? list![0]!.GrossFare : 0,
            },
            connectionAirport: [],
            isconnectionAirportExpand: false
        }
        list.forEach(x => {
            const arIndx = filter.Airlines.findIndex(ar => ar.Mac == x.MAC);
            if (arIndx == -1) {
                filter.Airlines.push({
                    Mac: x.MAC,
                    AirlineName: x.AirlineName && x.AirlineName.split('|')[1] || '',
                    isSelect: false
                })
            }
            if (x.GrossFare < filter.priceRange.Min) {
                filter.priceRange.Min = x.GrossFare;
            }
            if (x.GrossFare > filter.priceRange.Max) {
                filter.priceRange.Max = x.GrossFare;
                filter.priceRange.value = x.GrossFare;
            }
            x.Connections?.forEach(y => {
                const conIndx = filter.connectionAirport.findIndex(ca => ca.Airport == y.Airport);
                if (conIndx == -1) {
                    filter.connectionAirport.push({
                        Airport: y.Airport || '',
                        AirportName: y.ArrAirportName?.split('|')[1] || '',
                        isSelect: false
                    })
                }
            })

        });

        return filter;
    }

    addFilterInList(filterData: FlightFilterData | undefined, list: Journey[]) {

        let flightList = list;

        flightList.forEach(x => {
            let visble = false;
            filterData!.stop.forEach(st => {
                if (st.isSelect) {
                    if (st.value == 2 && x.Connections && x.Connections?.length >= 2) {
                        x.isVisible = true;
                        visble = true;
                    } else if (st.value == x.Connections?.length) {
                        x.isVisible = true;
                        visble = true;
                    } else {
                        if (!visble) x.isVisible = false;
                    }
                }
            })
            if (x.isVisible) {
                if (filterData!.Refund.isSelect) {
                    if (x.Refundable == 'Y') {
                        x.isVisible = true;
                    } else {
                        x.isVisible = false;
                    }
                }
            }
            if (x.isVisible) {
                let visble = false;
                filterData!.DepTime.forEach(dp => {
                    if (dp.isSelect) {
                        const time = new Intl.DateTimeFormat('en', {
                            hour: '2-digit',
                            hour12: false
                          }).format(new Date(x.DepartureTime));
                          
                        if (time) {
                            if (+time >= dp.min && +time <= dp.max) {
                                x.isVisible = true;
                                visble = true
                            } else {
                                if (!visble) x.isVisible = false;
                            }
                        }
                    }
                })
            }
            if (x.isVisible) {
                let visble = false;
                filterData!.ArrTime.forEach(dp => {
                    if (dp.isSelect) {
                        const time = new Intl.DateTimeFormat('en', {
                            hour: '2-digit',
                            hour12: false
                          }).format(new Date(x.DepartureTime));
                        if (time) {
                            if (+time >= dp.min && +time <= dp.max) {
                                x.isVisible = true;
                                visble = true;
                            } else {
                                if (!visble) x.isVisible = false;
                            }
                        }
                    }
                })
            }
            if (x.isVisible) {
                let visble = false;
                filterData!.Airlines.forEach(ar => {
                    if (ar.isSelect) {
                        if (ar.Mac == x.MAC) {
                            x.isVisible = true;
                            visble = true;
                        } else {
                            if (!visble) x.isVisible = false;
                        }
                    }
                })
            }
            if (x.isVisible) {
                if (filterData!.priceRange.Max != filterData!.priceRange.value) {
                    if (x.GrossFare <= filterData!.priceRange.value) {
                        x.isVisible = true;
                    } else {
                        x.isVisible = false;
                    }
                }
            }
            if (x.isVisible) {
                let visble = false;
                filterData!.connectionAirport.forEach(ca => {
                    if (ca.isSelect) {
                        const ind = x.Connections.findIndex(i => i.Airport == ca.Airport)
                        if (ind >= 0) {
                            x.isVisible = true;
                            visble = true;
                        } else {
                            if (!visble) x.isVisible = false;
                        }
                    }
                })
            }
        })


        return flightList.filter(x => x.isVisible);
    }
}