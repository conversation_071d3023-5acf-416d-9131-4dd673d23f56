"use client"

import { useEffect, useState } from "react"
import styles from './pages.module.scss'
import dynamic from "next/dynamic";
import { useRouter } from 'next/navigation'



const DyFlightSearchSection = dynamic(() => import("./Components/DyFlightSearchSection/DyFlightSearchSection"), { ssr: false });


export default function FlightHomePage() {
    const router = useRouter();


    function searchFlight() {
        router.push('FlightList')
    }


    return (
        <>
            <div className={styles['dy-flight-home-div']}>
                <h2>Best deals are waiting for you</h2>
                <div className={styles['dy-flight-post']}>
                    <div className="container">
                        <DyFlightSearchSection searchEmit={searchFlight}></DyFlightSearchSection>
                    </div>
                </div>

            </div>

            {/* <section className="bg-white dark:bg-gray-900">
                <div className="mx-auto max-w-screen-xl px-4 py-8 sm:py-16 lg:px-6">
                    <div className="justify-center space-y-8 md:grid md:grid-cols-2 md:gap-12 md:space-y-0 lg:grid-cols-3">
                        {LP_GRID_ITEMS.map((singleItem) => (
                            <div key={singleItem.title} className="flex flex-col items-center justify-center text-center">
                                <div className="mb-4 flex h-10 w-10 items-center justify-center rounded-full bttn_bg_home p-1.5 dark:bg-primary-900 lg:h-12 lg:w-12">
                                    {singleItem.icon}
                                </div>
                                <h3 className="mb-2 text-xl font-bold dark:text-white">{singleItem.title}</h3>
                                <p className="text-gray-500 dark:text-gray-400">{singleItem.description}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section> */}


            <div className="container">
                <div className={styles['top-deals-div']}>
                    <h3>Best deals <span className={styles['underline-mask']}></span></h3>

                    <div className={styles['top-deals-list']}>

                        <div className={styles['div1']}>
                            <img src="https://i.pinimg.com/564x/b7/15/94/b715944094db5f41aa5deed418a7d3f8.jpg" alt="" />
                            <div className={styles['card-content']}>
                                <h2>
                                    Card Heading
                                </h2>
                                <p>
                                    Lorem, ipsum dolor sit amet consectetur adipisicing elit. Nesciunt exercitationem iste, voluptatum, quia explicabo laboriosam rem adipisci voluptates cumque, veritatis atque nostrum corrupti ipsa asperiores harum? Dicta odio aut hic.
                                </p>
                            </div>
                        </div>

                        <div className={styles['div2']}>
                            <img src="https://images.stockcake.com/public/8/d/f/8df4e302-7a85-41e1-bdc3-da52d8a3ccb9_large/busy-airport-operations-stockcake.jpg" alt="" />
                            <div className={styles['card-content']}>
                                <h2>
                                    Card Heading
                                </h2>
                                <p>
                                    Lorem, ipsum dolor sit amet consectetur adipisicing elit. Nesciunt exercitationem iste, voluptatum, quia explicabo laboriosam rem adipisci voluptates cumque, veritatis atque nostrum corrupti ipsa asperiores harum? Dicta odio aut hic.
                                </p>
                            </div>
                        </div>

                        <div className={styles['div3']}>
                            <img src="https://st.depositphotos.com/1144687/3421/i/450/depositphotos_34217953-stock-photo-businessman-in-airport.jpg" alt="" />
                            <div className={styles['card-content']}>
                                <h2>
                                    Card Heading
                                </h2>
                                <p>
                                    Lorem, ipsum dolor sit amet consectetur adipisicing elit. Nesciunt exercitationem iste, voluptatum, quia explicabo laboriosam rem adipisci voluptates cumque, veritatis atque nostrum corrupti ipsa asperiores harum? Dicta odio aut hic.
                                </p>
                            </div>
                        </div>

                        <div className={styles['div4']}>
                            <img src="https://c1.wallpaperflare.com/preview/576/630/376/plane-delta-sunset-orange.jpg" alt="" />
                            <div className={styles['card-content']}>
                                <h2>
                                    Card Heading
                                </h2>
                                <p>
                                    Lorem, ipsum dolor sit amet consectetur adipisicing elit. Nesciunt exercitationem iste, voluptatum, quia explicabo laboriosam rem adipisci voluptates cumque, veritatis atque nostrum corrupti ipsa asperiores harum? Dicta odio aut hic.
                                </p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </>
    )
}