"use client"

import Add from '@mui/icons-material/Add'
import Check from '@mui/icons-material/Check'
import Remove from '@mui/icons-material/Remove'
import { useEffect, useState } from "react";
import { TravellersForm } from "models/flight-search-model"
import styles from './DyTravellerClass.module.scss'

interface DyTravellerClassProps {
    initialADT: number
    initialCHD: number
    initialINF: number
    initalTravellerClass: string
    isMobile: boolean;
    emitForm: (travellerForm: TravellersForm) => void

}

export default function DyTravellerClass({ initialADT = 1, initialCHD = 0, initialINF = 0, initalTravellerClass = 'E', isMobile = false, emitForm }: DyTravellerClassProps) {
    const [ADT, setADT] = useState(initialADT);
    const [CHD, setCHD] = useState(initialCHD);
    const [INF, setINF] = useState(initialINF);
    const [travellerClass, setTravellerClass] = useState(initalTravellerClass);

    useEffect(()=>{
        console.log('initialADT,initialCHD,initialINF',initialADT,initialCHD,initialINF)
    },[initialADT,initialCHD,initialINF])

    function addCount(type: string) {
        const tot = ADT + CHD
        if (tot < 9) {
            if (type === 'ADT') {
                setADT(ADT => ADT +1)
            }
            if (type === 'CHD') {
                setCHD(CHD => CHD +1)
            }
        }
        if (type === 'INF') {
            if(INF < ADT)
            setINF(INF => INF +1)
        }
    }

    function minusCount(type: string) {
        if (type === 'ADT') {
            if (ADT > 1) {
                setADT(ADT => ADT - 1)
            }
        }
        if (type === 'CHD') {
            if (CHD > 0) {
                setCHD(CHD => CHD - 1)
            }
        }
        if (type === 'INF') {
            if (INF > 0) {
                setINF(INF => INF - 1)
            }
        }
    }

    function selectClass(data: string) {
        setTravellerClass(data)
    }

    function submite() {
        emitForm({
            adult: ADT,
            child: CHD,
            infant: INF,
            travellerClass: travellerClass
        })
    }


    return (
        <>
            {(() => {
                if (!isMobile) {
                    return <div className={styles["dy_traveller_class_selection_overlay_div"]} onClick={submite} ></div >
                }
            })()}

            <div className={styles["dy_traveller_class_selection_div"]}>
                <div className={styles["count_div"]}>
                    <div className={styles["text_div"]}>Adults</div>
                    <div className={styles["bttn_grp"]}>
                        <button type="button"
                            className={styles["add_min_btn"]}
                            disabled={ADT === 1}
                            onClick={() => minusCount('ADT')}
                        >
                            <Remove />
                        </button>
                        <div className={styles["count_tct"]}>{ADT}</div>
                        <button type="button" className={styles["add_min_btn"]} onClick={() => addCount('ADT')} disabled={CHD+ADT === 9}><Add /></button>
                    </div>
                </div >

                <div className={styles["count_div"]}>
                    <div className={styles["text_div"]}>
                        Children
                        <span>2 - 12 Years</span>
                    </div>
                    <div className={styles["bttn_grp"]}>
                        <button
                            type="button"
                            className={styles["add_min_btn"]}
                            disabled={CHD === 0}
                            onClick={() => minusCount('CHD')}
                        >
                            <Remove />
                        </button>
                        <div className={styles["count_tct"]}>{CHD}</div>
                        <button
                            type="button"
                            className={styles["add_min_btn"]}
                            onClick={() => addCount('CHD')}
                            disabled={CHD+ADT === 9}
                        >
                            <Add />
                        </button>
                    </div>
                </div>

                <div className={styles["count_div"]}>
                    <div className={styles["text_div"]}>
                        Infants
                        <span>0 - 23 Months</span>
                    </div>
                    <div className={styles["bttn_grp"]}>
                        <button
                            type="button"
                            className={styles["add_min_btn"]}
                            disabled={INF === 0}
                            onClick={() => minusCount('INF')}
                        >
                            <Remove />
                        </button>
                        <div className={styles["count_tct"]}>{INF}</div>
                        <button
                            type="button"
                            className={styles["add_min_btn"]}
                            onClick={() => addCount('INF')}
                            disabled={ADT === INF}
                        >
                            <Add />
                        </button>
                    </div>
                </div>

                <div className={styles["travel_class_div"]}>
                    <div
                        className={`${styles['check_data']} ${travellerClass === 'E' ? styles['checked'] : ''} `} onClick={() => selectClass('E')} >
                        <div className={styles["check_box"]}>
                            {travellerClass === 'E' && <Check className={styles["checkicon"]}/>}
                        </div>
                        <div className={styles["text_div"]}>Economy</div>
                    </div>

                    <div
                        className={`${styles['check_data']} ${travellerClass === 'PE' ? styles['checked'] : ''}`}
                        onClick={() => selectClass('PE')}
                    >
                        <div className={styles["check_box"]}>
                            {travellerClass === 'PE' && <Check className={styles["checkicon"]}/>}
                        </div>
                        <div className={styles["text_div"]}>Premium Economy</div>
                    </div>

                    <div
                        className={`${styles['check_data']} ${travellerClass === 'B' ? styles['checked'] : ''}`}
                        onClick={() => selectClass('B')} >
                        <div className={styles["check_box"]}>
                            {travellerClass === 'B' && <Check className={styles["checkicon"]}/>}
                        </div>
                        <div className={styles["text_div"]}>Business</div>
                    </div >
                </div >

                <div className={styles["apply_bttn"]}>
                    <button className={styles["button-search"]} type="button" onClick={submite}>Apply</button>
                </div>

            </div >


        </>
    )
}