import React, { useEffect, useState } from 'react';
import styles from './SeatSelection.module.scss';

interface DisplayData {
  SeatNumber: string;
  SeatType: string;
  SeatStatus: string;
  displayXValue: string;
  displayYValue: string;
  Fare: number;
  select: boolean;
}

interface FlightSeat {
  from: string;
  to: string;
  selectedCount: number;
  selectedPrice: number;
  extend: boolean;
  xValueTop: number;
  yValueTop: number;
  displayDatas: DisplayData[];
}

interface FlightsSeats {
  title: string;
  values: FlightSeat[];
}

interface SeatSelectAddonsProps {
  flightsSeats: FlightsSeats;
  currency: string;
  expand: (id:number)=>void;
  onSeatSelect: (ssId: number, sId: number) => void;
}

export function SeatSelectAddons({ flightsSeats, currency, onSeatSelect,expand }: SeatSelectAddonsProps) {
  const [expandedIds, setExpandedIds] = useState<number[]>([]);
  
  let grideSpace:string = '30px';

  useEffect(()=>{
    if(window.innerWidth < 768){
      grideSpace = '25px';
    }else{
      grideSpace = '30px';
    }
  },[])


  function expandedValues(id:number){
    expand(id);
  }

  function selectSeat(segId:number,seatId:number){
    onSeatSelect(segId,seatId)
  }
  const toggleExpand = (ssId: number) => {
    setExpandedIds((prev) =>
      prev.includes(ssId) ? prev.filter((id) => id !== ssId) : [...prev, ssId]
    );
  };

  const FlightCurrencyConverter = (amount: number): string => {
    // Placeholder for currency conversion logic
    return `${amount} ${currency}`;
  };

  const seatTypeLabel = (seatType: string): string => {
    switch (seatType) {
      case 'SM':
        return 'Spice Max';
      case 'AACIN':
        return 'Blocked';
      case 'ees':
        return 'Emergency Row';
      case 'PS':
        return 'Preferred';
      default:
        return 'Others';
    }
  };

  return (
    <div className={styles["seat-select-addons-div"]}>
      {flightsSeats?.values?.map((flight, ssId) => (
        <div className={styles["card-one-div"]} key={ssId}>
          <div className={styles["head-div"]} onClick={() => toggleExpand(ssId)}>
            <h3>{flight?.from}-{flight?.to}</h3>
            {flight?.selectedCount > 0 && (
              <div className={styles["select-count-price-div"]}>
                {flight?.selectedCount} {flightsSeats?.title} Added <strong>{FlightCurrencyConverter(flight?.selectedPrice)}</strong>
              </div>
            )}
            <h3>{expandedIds.includes(ssId) ? '-' : '+'}</h3>
          </div>

          {expandedIds.includes(ssId) && (
            <div className={styles["expand-div"]}>
              <div className={styles["seat-asign-div"]}>
                <div className={styles["seat-name-txt"]}>
                  <div className={styles["seat-assign"]}>
                    <div className={styles["seat your-seat"]}></div>
                  </div>
                  <div className={styles["seat-txt"]}>Your Seat</div>
                </div>
                <div className={styles["seat-name-txt"]}>
                  <div className={styles["seat-assign"]}>
                    <div className={styles["seat spice-max"]}></div>
                  </div>
                  <div className={styles["seat-txt"]}>Spice Max</div>
                </div>
                <div className={styles["seat-name-txt"]}>
                  <div className={styles["seat-assign"]}>
                    <div className={styles["seat reserved"]}></div>
                  </div>
                  <div className={styles["seat-txt"]}>Reserved</div>
                </div>
                <div className={styles["seat-name-txt"]}>
                  <div className={styles["seat-assign"]}>
                    <div className={styles["seat aacin"]}></div>
                  </div>
                  <div className={styles["seat-txt"]}>Blocked/Available at Airport Check-in/Occupied</div>
                </div>
                <div className={styles["seat-name-txt"]}>
                  <div className={styles["seat-assign"]}>
                    <div className={styles["seat others"]}></div>
                  </div>
                  <div className={styles["seat-txt"]}>Others</div>
                </div>
                <div className={styles["seat-name-txt"]}>
                  <div className={styles["seat-assign"]}>
                    <div className={styles["seat emergency"]}></div>
                  </div>
                  <div className={styles["seat-txt"]}>Emergency Row</div>
                </div>
                <div className={styles["seat-name-txt"]}>
                  <div className={styles["seat-assign"]}>
                    <div className={styles["seat preferred"]}></div>
                  </div>
                  <div className={styles["seat-txt"]}>Preferred</div>
                </div>
              </div>

              <div className={styles["seat-selection-div"]}>
                <div className={styles["flight-div"]}>
                  <div className={styles["top-section"]}>
                    <div className={styles["cockpit"]}><h2>Cockpit</h2></div>
                  </div>
                  <div
                    className={styles["seat-selection-section"]}
                    style={{
                      gridTemplateColumns: `repeat(${flight?.xValueTop}, 1fr)`,
                      gridTemplateRows: `repeat(${flight?.yValueTop}, 1fr)`,
                    }}
                  >
                    {flight?.displayDatas?.map((seat, sId) => (
                      <div
                        key={sId}
                        className={`seat ${seat?.select ? 'your-seat' : 
                          seat?.SeatStatus === 'Reserved' ? 'reserved' : 
                          seat?.SeatType === 'SM' ? 'spice-max' : 
                          seat?.SeatType === 'AACIN' ? 'aacin' : 
                          seat?.SeatType === 'ees' ? 'emergency' : 
                          seat?.SeatType === 'PS' ? 'preferred' : 'others'}`}
                        style={{
                          gridColumn: seat?.displayXValue,
                          gridRow: seat?.displayYValue,
                        }}
                        title={`Seat No: ${seat?.SeatNumber} | Seat Type: ${seatTypeLabel(seat?.SeatType)} | Fare: ${FlightCurrencyConverter(seat?.Fare)}`}
                        onClick={() => onSeatSelect(ssId, sId)}
                      >
                        {seat?.SeatNumber}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

export default SeatSelectAddons;