import axios from 'axios';
import {
  AirportList,
  AirportListResponse,
  AirportSearchBody,
  ExpressSearchBody,
  GetExpressSearchBody,
  ScheduleBody,
  WebSettings
} from '../models/flight-search-model';
import {
  FlightFareRuleBody,
  FlightFareRuleResponse,
  FlightInfoBody,
  FlightInfoResponse,
  FlightSsrBody,
  FlightSsrResponse
} from '../models/flight-info-ssr-model';
import { FlightResponse } from 'models/flight-list-response-model';
import { BookNowBody, BookingList, DetailedBookingResponse } from 'models/flight-booking-models';

// Base configuration for axios
const axiosInstance = axios.create({
  baseURL: 'https://app.digiyatra.in/apis/',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
let accessToken: string | null = null;

// Initialize token from localStorage on app start
const initializeToken = () => {
  if (typeof window !== 'undefined') {
    const storedToken = localStorage.getItem('dyAccessToken');
    if (storedToken) {
      accessToken = storedToken;
    }
  }
};

// Initialize token when the module loads
initializeToken();

export const setAccessToken = (token: string) => {
  accessToken = token;
  // Also store in localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem('dyAccessToken', token);
  }
};

export const getAccessToken = (): string | null => {
  // If token is not in memory, try to get it from localStorage
  if (!accessToken && typeof window !== 'undefined') {
    accessToken = localStorage.getItem('dyAccessToken');
  }
  return accessToken;
};

export const clearAccessToken = () => {
  accessToken = null;
  // Also remove from localStorage
  if (typeof window !== 'undefined') {
    localStorage.removeItem('dyAccessToken');
    localStorage.removeItem('dyUser');
  }
};

// Request interceptor to add Bearer token to all requests except auth endpoints
axiosInstance.interceptors.request.use(
  (config) => {
    // Define auth endpoints that shouldn't include the token
    const authEndpoints = [
      'auth/register/',
      'auth/login/',
      'auth/otp_verify',
      'auth/login'
    ];

    // Check if the current request URL matches any auth endpoint
    const isAuthEndpoint = authEndpoints.some(endpoint =>
      config.url?.includes(endpoint)
    );

    // Get the current token (this will check localStorage if needed)
    const currentToken = getAccessToken();

    // Add Bearer token if not an auth endpoint and token exists
    if (!isAuthEndpoint && currentToken) {
      config.headers.Authorization = `Bearer ${currentToken}`;
      console.log('Adding Bearer token to request:', config.url);
    } else if (!isAuthEndpoint) {
      console.warn('No access token available for request:', config.url);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Optional: Response interceptor to handle token expiration
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized - token might be expired
    if (error.response?.status === 401) {
      clearAccessToken();
      // You can redirect to login page or show login modal here
      console.warn('Access token expired or invalid');
    }
    return Promise.reject(error);
  }
);

// Flight API Service Utility
const FlightApiService = {
  // Token management methods
  setToken: setAccessToken,
  getToken: getAccessToken,
  clearToken: clearAccessToken,

  searchAirport: async (body: AirportSearchBody): Promise<AirportList[]> => {
    const response = await axiosInstance.post<AirportList[]>('airports/', body);
    return response.data;
  },

  callExpressSearch: async (body: ExpressSearchBody | ScheduleBody): Promise<FlightResponse> => {
    const response = await axiosInstance.post<FlightResponse>('search/', body);
    return response.data;
  },

  getExpressSearch: async (body: GetExpressSearchBody): Promise<FlightResponse> => {
    const response = await axiosInstance.post<FlightResponse>('search_list/', body);
    return response.data;
  },

  getFlightInfo: async (body: FlightInfoBody): Promise<FlightInfoResponse> => {
    const response = await axiosInstance.post<FlightInfoResponse>('details/', body);
    return response.data;
  },

  getFlightSSR: async (body: FlightSsrBody): Promise<FlightSsrResponse> => {
    const response = await axiosInstance.post<FlightSsrResponse>('service_req/', body);
    return response.data;
  },

  getFlightFareRule: async (body: FlightFareRuleBody): Promise<FlightFareRuleResponse> => {
    const response = await axiosInstance.post<FlightFareRuleResponse>('rules/', body);
    return response.data;
  },

  getWebSettings: async (): Promise<WebSettings> => {
    const body = { "ClientID": "", "TUI": "" };
    const response = await axiosInstance.post<WebSettings>('setup/', body);
    return response.data;
  },

  getRetrieveBooking: async (body: any): Promise<any> => {
    const response = await axiosInstance.post('rb/', body);
    return response.data;
  },

  getTripValues: async (): Promise<any> => {
    const response = await axiosInstance.get('assets/jsons/flightSmartPricer.json');
    return response.data;
  },

  callSmartPricer: async (body: any): Promise<any> => {
    const response = await axiosInstance.post('pricing/', body);
    return response.data;
  },

  callGetsPrice: async (body: any): Promise<any> => {
    const response = await axiosInstance.post('pricing_list/', body);
    return response.data;
  },

  callTravelChecklist: async (body: any): Promise<any> => {
    const response = await axiosInstance.post('fetchservice/', body);
    return response.data;
  },

  callFlightSSR: async (body: any): Promise<any> => {
    const response = await axiosInstance.post('airline/SSR/', body);
    return response.data;
  },

  callFlightSeat: async (body: any): Promise<any> => {
    const response = await axiosInstance.post('seat/', body);
    return response.data;
  },

  // Auth methods (no token required)
  registerSubmit: async (body: any): Promise<{id:number,email:string}> => {
    const response = await axiosInstance.post('auth/register/', body);
    return response.data;
  },

  loginSubmit: async (body: {email: string , userId:number | null}): Promise<{id:number,email:string}> => {
    const response = await axiosInstance.get(`auth/get_login_otp/${body.userId}`);
    return response.data;
  },

  verifyOtp: async (body: {email: string, otp: string}): Promise<{message: string, verified: boolean, access_token?: string}> => {
    const response = await axiosInstance.post(`auth/otp_verify`, body);

    // Automatically set the token if verification is successful
    if (response.data.verified && response.data.access_token) {
      setAccessToken(response.data.access_token);
    }

    return response.data;
  },

  verifyLoginOtp: async (body: {email: string, otp: string}): Promise<{message: string, verified: boolean, access_token?: string}> => {
    const response = await axiosInstance.post(`auth/login?email=${body.email}&otp=${body.otp}`, body);

    // Automatically set the token if login is successful
    if (response.data.verified && response.data.access_token) {
      setAccessToken(response.data.access_token);
    }

    return response.data;
  },

  bookNowApi: async (body: BookNowBody): Promise<any> => {
    const response = await axiosInstance.post('create-booking/', body);
    return response.data;
  },

  getBookingList: async (): Promise<BookingList[]> => {
    // const body = userId ? { user_id: userId } : {};
    const response = await axiosInstance.get<BookingList[]>('user-bookings/');
    return response.data;
  },

  getBookingDetails: async (bookingReference: string): Promise<DetailedBookingResponse> => {
    const response = await axiosInstance.get<DetailedBookingResponse>(`get-booking/${bookingReference}`);
    return response.data;
  },
};

export default FlightApiService;