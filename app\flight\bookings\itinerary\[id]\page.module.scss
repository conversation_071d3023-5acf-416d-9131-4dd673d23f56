@import '../../../../../styles/variable.scss';

.itineraryPage {
  padding: 15px 0;
  min-height: calc(100vh - 120px);
  background: $bg_light;

  @media screen and (max-width: $isMobile) {
    padding: 12px 0;
  }
}

.pageHeader {
  max-width: 1200px;
  margin: 0 auto 20px auto;
  padding: 0 15px;

  @media screen and (max-width: $isMobile) {
    padding: 0 10px;
    margin-bottom: 15px;
  }

  h1 {
    font-size: 1.8rem;
    font-weight: 600;
    color: $primary_color;
    margin: 0;

    @media screen and (max-width: $isMobile) {
      font-size: 1.4rem;
    }
  }

  p {
    font-size: 13px;
    color: $blackgrey;
    margin: 4px 0 0 0;
  }
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: $white;
  border: 1px solid $bordercolor;
  color: $primary_color;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: $radius;
  transition: all 0.1s ease-in;
  text-decoration: none;
  margin-bottom: 15px;

  &:hover {
    background: $bg_light_color;
    box-shadow: 0 2px 3px rgba(142, 141, 141, 0.4);
  }

  @media screen and (max-width: $isMobile) {
    font-size: 13px;
    padding: 6px 10px;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;

  .loader {
    font-size: 1.1rem;
    color: $primary_color;
  }
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.errorMessage {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 500px;

  h3 {
    color: #dc3545;
    margin-bottom: 15px;
    font-size: 1.5rem;
  }

  p {
    color: #6c757d;
    margin-bottom: 25px;
    font-size: 1rem;
  }
}

.errorActions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;

  @media screen and (max-width: 480px) {
    flex-direction: column;
    align-items: center;
  }
}
