'use client';
import { ReactNode } from 'react';
import { Footer } from 'components/Footer/Footer';
import { Header } from 'components/Header/Header';
import { FlightStateProvider } from 'services/flight-state-service';

export default function FlightsLayout({ children }: { children: ReactNode }) {
  return (
    <>
      <div className='bg-gradient-to-br from-yellow-50 via-white to-yellow-100'>
      <Header />
      <div style={{ minHeight: "calc(100dvh - 40px)" }} >
        <FlightStateProvider>{children}</FlightStateProvider>
      </div>
      <Footer />
      </div>
      
    </>
  );
}