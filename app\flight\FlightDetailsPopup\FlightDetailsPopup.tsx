"use client";

import styles from './FlightDetailsPopup.module.scss';
import { useEffect, useState } from 'react';
import { FlightInfo } from './FlightInfo/FlightInfo';
import { FareSummary } from './FareSummary/FareSummary';
import { FareSummaryDetails, FlightBaggageInfo, FlightDetailsEmitData, FlightFareRuleTrip, FlightInfoMap } from 'models/flight-info-ssr-model';
import FlightApiService from 'services/flight-api-service';
import { FlightInfoFareRuleHelper } from 'helpers/flight-info-fare-rule-helper';
import { useSyncState } from 'helpers/sync-state';
import { FlightDetailsPopupShimmer } from './FlightDetailPopupShimmer/FlightDetailPopupShimmer';
import { FareRuleComponent } from './FareRule/FareRule.module';
import { FlightBaggageInfoComponent } from './BaggageInfo/BaggageInfo';
interface AirlineDetailsPopupProps {
  isMobile: boolean;
  currency: string;
  flightDetailsBody: FlightDetailsEmitData
}

export function AirlineDetailsPopup({ isMobile = false, currency = 'INR', flightDetailsBody }: AirlineDetailsPopupProps) {
  const [detailsBody, setDetailsBody, detailsBodyRef] = useSyncState<FlightDetailsEmitData | undefined>(undefined);
  const [shimmerFlightInfo, setShimmerFlightInfo] = useState<boolean>(true)
  const [shimmerSSR, setShimmerSSR] = useState<boolean>(true)
  const [shimmerFareRule, setShimmerFareRule] = useState<boolean>(true)
  const [adult, setAdult, adultRef] = useSyncState<number>(1)
  const [child, setChild, childRef] = useSyncState<number>(0)
  const [infant, setInfant, infantRef] = useSyncState<number>(0)

  const [flightInfo, setFlightInfo, flightInfoRef] = useSyncState<FlightInfoMap[]>([])
  const [fareSummaryArray, setFareSummaryArray, fareSummaryArrayRef] = useSyncState<FareSummaryDetails[]>([])
  const [fareSummary, setFareSummary] = useState<FareSummaryDetails | undefined>()
  const [fareRule, setFareRule, fareRuleRef] = useSyncState<FlightFareRuleTrip[]>([])
  const [flightBaggage, setFlightBaggage, flightBaggageRef] = useSyncState<FlightBaggageInfo[]>([])
  const [currentIndex, setCurrentIndex] = useState<number>(1);

  useEffect(() => {
    console.log("INSIDE USE EFFECT", flightDetailsBody)
    setDetailsBody(flightDetailsBody)
    initialCall()
  }, [flightDetailsBody])

  const api = FlightApiService;


  const helper = new FlightInfoFareRuleHelper();


  function initialCall() {
    console.log("INITIAL CALL")
    setShimmerFlightInfo(true)
    setShimmerSSR(true)
    setShimmerFareRule(true)
    setFlightInfo([])
    setFareSummaryArray([])
    setFareRule([])
    setFlightBaggage([])

    getFlightInfoApi(0);
    getFlightSSRApi(0);
    getFlightFareRuleApi(0);
  }

  async function getFlightInfoApi(index: number) {
    const bodyInfo = {
      "ClientID": "",
      "TripType": detailsBodyRef.current!.TripType!,
      "Trips": detailsBodyRef.current!.Trips[index]!
    }

    try {
      const dataInfo: any = await api.getFlightInfo(bodyInfo);
      setAdult(dataInfo.ADT)
      setChild(dataInfo.CHD)
      setInfant(dataInfo.INF)

      setFlightInfo(flightInfoRef.current.concat(helper.setTripSegments(dataInfo.Trips)))
      const tFareSummaryArray = [...fareSummaryArrayRef.current]
      tFareSummaryArray.push(helper.setFareSummary(dataInfo.Trips, adultRef.current, childRef.current, infantRef.current));
      setFareSummaryArray(tFareSummaryArray)

      if (detailsBodyRef.current) {

        if (index + 1 < detailsBodyRef.current.Trips.length) {
          getFlightInfoApi(index + 1)
        } else {
          if (fareSummaryArrayRef.current.length > 1) {
            // const f = helper.addFareSummaries(fareSummaryArrayRef.current)
            // setFareSummary(f)
          } else {
            setFareSummary(fareSummaryArrayRef.current[0])
          }
          setShimmerFlightInfo(false)
        }
      }
    } catch (error) {

    }

  }

  async function getFlightSSRApi(index: number) {
    const bodySSR = {
      "ClientID": "",
      "PaidSSR": false,
      "Source": "LV",
      "Trips": detailsBodyRef.current!.Trips[index]!
    }
    console.log('getFlightSSRApi',index,'index', bodySSR)

    try {
      const data = await api.getFlightSSR(bodySSR);
      if (data.Trips && data.Trips[0] && data.Trips[0].From) {
        const tFlightBaggage = [...flightBaggageRef.current]
        setFlightBaggage(tFlightBaggage.concat(helper.setBaggageValueFromSSRResponse(data.Trips)))
      }
      if (detailsBodyRef.current) {
        if (index + 1 < detailsBodyRef.current.Trips.length) {
          getFlightSSRApi(index + 1);
        } else {
          setShimmerSSR(false)
        }

      }
    } catch (error) {

    }

  }

  async function getFlightFareRuleApi(index: number) {
    const bodyFareRule = {
      "ClientID": "",
      "Source": "SF",
      "Trips": detailsBodyRef.current?.Trips[index]!
    }

    try {
      const data = await api.getFlightFareRule(bodyFareRule)
      if (data.Trips) {
        const tFareRule = [...fareRuleRef.current].concat(data.Trips)
        setFareRule(tFareRule)
      }
      if (detailsBodyRef.current) {
        if (index + 1 < detailsBodyRef.current.Trips.length) {
          getFlightFareRuleApi(index + 1);
        } else {
          setShimmerFareRule(false)
        }
      }
    } catch (error) {

    }
  }


  return (
    <>
      <div className={styles["flight-details-popup-div"]}>
        <div className={styles['tab-menu-container']}>
          <div onClick={() => setCurrentIndex(1)} className={`${styles['tab-menu-item']} ${currentIndex === 1 ? styles.active : ''}`}>Overview</div>
          <div onClick={() => setCurrentIndex(2)} className={`${styles['tab-menu-item']} ${currentIndex === 2 ? styles.active : ''}`}>Fare Summary & Rules</div>
          <div onClick={() => setCurrentIndex(3)} className={`${styles['tab-menu-item']} ${currentIndex === 3 ? styles.active : ''}`}>Baggage & Inclusions</div>
        </div>
        {currentIndex === 1 && (
          <div className={styles['common-pdng-flight-details']}>
            {shimmerFlightInfo ? (
              <FlightDetailsPopupShimmer type='fareinfo' />
            ) : (
              flightInfo.map((flight: any, index: number) => (
                <FlightInfo key={index} flight={flight} />
              ))
            )}
          </div>
        )}
        {currentIndex === 2 && (
          <div className={styles['fare-summary-fare-rule-div']}>
            <div className={styles['fare-summary-div']}>
              {shimmerFlightInfo ? (
                <FlightDetailsPopupShimmer type='faresummary' />
              ) : (
                <FareSummary fareSummary={fareSummary} currency={currency} />
              )}
            </div>
            <div className={styles['fare-rule-div']}>
              {shimmerFareRule ? (
                <FlightDetailsPopupShimmer type='farerule' />
              ) : (
                <FareRuleComponent fareRuleList={fareRule} currency={currency} adult={adult} child={child} infant={infant} />
              )}
            </div>
          </div>
        )}
        {currentIndex === 3 && (
          <div className={styles['baggage-inclusion-div']}>
            {shimmerSSR ? (
              <FlightDetailsPopupShimmer type='baggage' />
            ) : (
              <FlightBaggageInfoComponent flightBaggage={flightBaggage} />
            )}
          </div>
        )}

      </div>
    </>
  );
}