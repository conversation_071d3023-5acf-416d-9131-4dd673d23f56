import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import NotificationImportantIcon from "@mui/icons-material/NotificationImportant";
import SearchIcon from "@mui/icons-material/Search";
import { useCallback , useEffect, useRef, useState } from "react";
import { AirportList } from "models/flight-search-model";
import FlightApiService from "services/flight-api-service";
import styles from "./DyAirportSelection.module.scss";

interface DyAirportSelectionProps {
  location?: string;
  close?: () => void;
  selectedAirport: (airport: AirportList) => void;
  iata?: string;
  isMobile?: boolean;
  isOpen?: boolean;
}

export default function DyAirportSelection({
  location = "from",
  close = () => {},
  selectedAirport,
  // iata = "",
  isOpen = false,
  isMobile = false,
}: DyAirportSelectionProps) {
  const [isPreviouslySearchData, setIsPreviouslySearchData] = useState<boolean>(false);
  const [shimmer, setShimmer] = useState<boolean>(true);
  const [airportList, setAirportList] = useState<AirportList[]>([]);
  const [previouslySearch, setPreviouslySearch] = useState<AirportList[]>([]);
  const [airportControl, setAirportControl] = useState<string>("");
  const selectAirportInputRef = useRef<HTMLInputElement | null>(null);

  // Fetch airports from API on component mount
    // Load previously searched airports from localStorage or API
    const loadPreviousAirports = useCallback(() => {
      const storedAirports = localStorage.getItem("dyPreviousSearchAirports");
      if (storedAirports) {
        const parsedAirports = JSON.parse(storedAirports) as AirportList[];
        setPreviouslySearch(parsedAirports);
        setIsPreviouslySearchData(parsedAirports.length > 0);
        setAirportList(parsedAirports);
        setShimmer(false);
      } else {
        fetchAirportsFromApi();
      }
    }, []);
  
  useEffect(() => {
    if(!isMobile){
      const timer = setTimeout(() => {
        selectAirportInputRef.current?.focus();
      }, 5);
  
      loadPreviousAirports();
    
      return () => clearTimeout(timer);
    }

  }, [loadPreviousAirports,isMobile]);

  useEffect(() => {
    if(isOpen){
      const timer = setTimeout(() => {
        selectAirportInputRef.current?.focus();
      }, 100);
  
      loadPreviousAirports();
    
      return () => clearTimeout(timer);
    }
  }, [isOpen,loadPreviousAirports]);




  // Fetch airports from API
  const fetchAirportsFromApi = async () => {
    setIsPreviouslySearchData(false);
    setShimmer(true);
    try {
      const data = await FlightApiService.searchAirport({ search_text: "" });
      
      const indianAirports = data.filter((x: AirportList) => x.country === "India");
  
      setAirportList(indianAirports);
      setPreviouslySearch(indianAirports);
      localStorage.setItem("dyPreviousSearchAirports", JSON.stringify(indianAirports));
      
    } catch (error) {
      console.error("Error fetching airports:", error);
    } finally {
      setShimmer(false);
    }
  };

  // Handle input change and trigger search
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAirportControl(value);
    if (value.trim() === "") {
      setAirportList(previouslySearch);
      return;
    }
    searchAirport(value);
  };

  const mergeUniqueAirports = useCallback((newAirports: AirportList[]) => {
    setPreviouslySearch((prev) => {
      const combinedArray = [...newAirports, ...prev];
      const uniqueArray = combinedArray.filter(
        (item, index, self) =>
          index === self.findIndex((t) => t.code === item.code)
      );
      return uniqueArray;
    });
  }, []);
  

  const fetchSearchResultsFromApi = useCallback(async (value: string) => {
    setShimmer(true);
    try {
      const data = await FlightApiService.searchAirport({ search_text: value });
      const indianAirports = data.filter((x: AirportList) => x.country === "India");
      setAirportList(indianAirports);
      mergeUniqueAirports(indianAirports);
    } catch (error) {
      console.error("Error fetching search results:", error);
    } finally {
      setShimmer(false);
    }
  }, [setShimmer, setAirportList, mergeUniqueAirports]);
  

  // Search for airports based on input value
  const searchAirport = useCallback((value: string) => {
    const filteredAirports = previouslySearch.filter(
      (airport) =>
        airport.code.toLowerCase().includes(value.toLowerCase()) ||
        airport.city_name.toLowerCase().includes(value.toLowerCase()) ||
        airport.name.toLowerCase().includes(value.toLowerCase())
    );

    if (filteredAirports.length > 0) {
      setIsPreviouslySearchData(false);
      setAirportList(filteredAirports);
    } else {
      fetchSearchResultsFromApi(value);
    }
  }, [previouslySearch,fetchSearchResultsFromApi]);

  const handleClose = ()  => {
    close()
    setAirportControl('')
    setAirportList(previouslySearch)
  }


  // Select an airport
  const selectAirport = (airport: AirportList) => {
    saveAirportToLocalStorage(airport);
    selectedAirport(airport);
  };

  // Save selected airport to localStorage
  const saveAirportToLocalStorage = (airport: AirportList) => {
    const updatedList = previouslySearch.filter((item) => item.code !== airport.code);
    updatedList.unshift(airport);
    if (updatedList.length > 50) {
      updatedList.slice(0, 50);
    }
    setPreviouslySearch(updatedList);
    localStorage.setItem("dyPreviousSearchAirports", JSON.stringify(updatedList));
  };

  return (
    <>
      {!isMobile && <div className={styles.airport_select_close_div} onClick={handleClose}></div>}

      <div className={styles["airport_auto_complete"]}>
        <div className={styles["search_div"]}>
          {!isMobile ? (
            <SearchIcon />
          ) : (
            <KeyboardArrowLeftIcon onClick={handleClose} style={{ cursor: 'pointer' }} />
          )}
          <input
            type="text"
            className={styles.input_text_data}
            placeholder={location === "from" ? "Leaving from" : "Going to"}
            value={airportControl}
            onChange={handleInputChange}
            ref={selectAirportInputRef}
          />
        </div>

        <div className={styles.search_list_div}>
          {shimmer ? (
            <div className={styles.search_data_div}>
              {Array.from({ length: 9 }).map((_, index) => (
                <div key={index} className="h-4 bg-slate-200 rounded col-span-2 mb-2"></div>
              ))}
            </div>
          ) : isPreviouslySearchData ? (
            <>
              <div className={styles.previously_search_head}>Recent Airports</div>
              {previouslySearch.map((airport) => (
                <div
                  key={airport.code}
                  className={styles.search_data_div}
                  onClick={() => selectAirport(airport)}
                >
                  <div className={styles.airport_data}>
                    <div className={styles.city_airpt_name}>
                      <h2 className={styles.city_name}>{airport.city_name}</h2>
                      <div className={styles.airpt_name}>{airport.name}</div>
                    </div>
                    <div className={styles.iata_name}>
                      <div className={styles.iata_card}>{airport.code}</div>
                    </div>
                  </div>
                </div>
              ))}
            </>
          ) : airportList.length > 0 ? (
            airportList.map((airport) => (
              <div
                key={airport.code}
                className={styles.search_data_div}
                onClick={() => selectAirport(airport)}
              >
                <div className={styles.airport_data}>
                  <div className={styles.city_airpt_name}>
                    <h2 className={styles.city_name}>{airport.city_name}</h2>
                    <div className={styles.airpt_name}>{airport.name}</div>
                  </div>
                  <div className={styles.iata_name}>
                    <div className={styles.iata_card}>{airport.code}</div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className={styles.error_airport}>
              <NotificationImportantIcon className={`material-icons ${styles.icon_er}`} />
              Exclusively for Indian airports.
            </div>
          )}
        </div>
      </div>
    </>
  );
}
