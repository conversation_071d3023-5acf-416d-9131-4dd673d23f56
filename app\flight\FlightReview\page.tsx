"use client"

import InfoIcon from '@mui/icons-material/Info';
import { useRouter } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import FlightReviewPageAdapter from 'adapters/flight-review-adapter';
import AirlineItineryCard from 'components/AirlineItineryCard/AirlineItineryCard';
import DyBottomUpPopup from 'components/DyBottomUpPopup/DyBottomUpPopup';
import { FlightSSRAddonsHelper } from 'helpers/flight-review-addon-adapter';
import FlightReviewPageHelper from 'helpers/flight-review-helper';
import { useSyncState } from 'helpers/sync-state';
// import { SmartPricerRequest } from 'models/smart-pricer-response';
import FlightApiService from 'services/flight-api-service';
import ShimmerComponent from './FlightReviewLoader/FlightReviewLoader';
import styles from './page.module.scss';

// Placeholder components – replace with actual components in your codebase
import FlightReviewFareSummary from './ReviewFareSummary/ReviewFareSummary';
import FlightReviewTravellerForm, { travel_form } from './TravelForm/TravelForm';
import { Loader } from 'components/loader/loader';
import { BookNowBody } from 'models/flight-booking-models';

const FlightReview = () => {
  const [isMobile, setIsMobile] = useState<boolean>(false);
  // const [smartPrice, setSmartPrice] = useState<SmartPricerRequest>();
  // const [TUIarr, setTUIarr] = useState<string[]>([]);
  const [smartPriceLoader, setSmartPriceLoader] = useState(true);
  const [cardShimmer, setCardShimmer] = useState(true);
  const [fareSummaryLoader, setFareSummaryLoader] = useState(true);
  // const [getsShimmer, setGetsShimmer] = useState(true);
  const [travellerShimmer, setTravellerShimmer] = useState(true);
  const [addonsShimmer, setAddonsShimmer] = useState(true);
  const [showPriceSummary, setShowPriceSummary] = useState(false);
  const [getsPriceData, setGetsPriceData,getsPriceDataRef] = useSyncState<any[]>([]);
  const [fareSummaryArray, setFareSummaryArray, fareSummaryArrayRef] = useSyncState<any[]>([]);
  const [fareSummary, setFareSummary] = useState<any>();
  const [flightAddons, setFlightAddons] = useState<any>({ ssr: [], seat: [], trip: [] });
  const [ssrData, setSsrData] = useState<any[]>([]);
  const [emptyArray, setEmptyArray] = useState(false);
  // const [addonsData, setAddonsData] = useState<any>();
  const [currency, setCurrency] = useState('INR');
  const [requestObj, setRequestObj, requestObjRef] = useSyncState<[]>([]);
  const [getsPriceTuis, setGetsPriceTuis, getsPriceTuisRef] = useSyncState<any[]>([]);
  const [travellCheckLists, setTravellCheckLists, travellerCheckListRef] = useSyncState<any[]>([]);
  const [sectType, setSectType] = useState<string | null>('D');
  // const [demoPopUp, setDemoPopUp] = useState(false);
  const [travelForm, setTravelForm,travelFormRef] = useSyncState<any>({});
  const [travellerCheckList, setTravellerCheckList] = useState<any>();
  const [isLoad, setIsLoad] = useState<boolean>(false);

  // const history = useHistory();
  const helper = useMemo(() => new FlightReviewPageHelper(), []);
  const adapter = useMemo(() => new FlightReviewPageAdapter(), []);
  const adonsHelper = useMemo(() => new FlightSSRAddonsHelper(), []);

  useEffect(()=>{
    setCurrency("INR")
    setShowPriceSummary(false)
  },[])

  const api = FlightApiService;
  const router = useRouter();

  const isInitialRender = useRef(true);

  const setAddonsDataFinal = useCallback(() => {
    const updatedSSRData = adonsHelper.addonsDataSet(flightAddons);
    setSsrData(updatedSSRData);
    setEmptyArray(updatedSSRData.every(service => service.values.length === 0));
    setAddonsShimmer(false);
  }, [adonsHelper, flightAddons, setSsrData, setEmptyArray, setAddonsShimmer]);

  const changeFlight = useCallback(() => {
    router.back();
  }, [router]);

  const callFlightSeatApi = useCallback(async (i: number): Promise<void> => {
    if (
      adonsHelper.isCheckFlightSeatApiCall(
        sectType!,
        getsPriceDataRef.current[i].Trips[0].Journey[0].Segments[0].Flight.MAC
      )
    ) {
      const body: any = {
        Source: getsPriceDataRef.current[i].Source,
        Trips: getsPriceDataRef.current[i].Trips.map((x: any, j: number) => ({
          Index: "",
          OrderID: j + 1,
          TUI: getsPriceTuisRef.current[i],
        })),
      };

      try {
        const data = await api.callFlightSeat(body);
        setFlightAddons((prev: any) => ({
          ...prev,
          seat: [...prev.seat, data],
        }));

        if (i + 1 === getsPriceTuisRef.current.length) {
          setAddonsDataFinal();
        } else {
          await callFlightSeatApi(i + 1);
        }
      } catch (error) {
        setAddonsDataFinal();
      }
    } else {
      setAddonsDataFinal();
    }
  }, [
    adonsHelper,
    sectType,
    getsPriceDataRef,
    getsPriceTuisRef,
    api,
    setFlightAddons,
    setAddonsDataFinal,
  ]);


  const callFlightSSRApi = useCallback(async (i: number): Promise<void> => {
    if (
      adonsHelper.isCheckSSRApiCall(
        sectType!,
        getsPriceDataRef.current[i].Trips[0].Journey[0].Segments[0].Flight.MAC
      )
    ) {
      const body: any = {
        PaidSSR: true,
        Source: getsPriceDataRef.current[i].Source,
        Trips: getsPriceDataRef.current[i].Trips.map((x: any, j: number) => ({
          Amount: 0,
          Index: "",
          OrderID: j + 1,
          TUI: getsPriceTuisRef.current[i],
        })),
      };

      try {
        const data = await api.callFlightSSR(body);
        setFlightAddons((prev: any) => ({
          ...prev,
          ssr: [...prev.ssr, data],
        }));

        if (i + 1 === getsPriceTuisRef.current.length) {
          callFlightSeatApi(0);
        } else {
          await callFlightSSRApi(i + 1);
        }
      } catch (error) {
        // Handle error as needed
      }
    } else {
      callFlightSeatApi(0);
    }
  }, [
    adonsHelper,
    sectType,
    getsPriceDataRef,
    getsPriceTuisRef,
    api,
    setFlightAddons,
    callFlightSeatApi,
  ]);


  const callTravellCheckListApi = useCallback(async (i: number): Promise<void> => {
    try {
      const data = {
        TUI: "ON36eb6188-84a1-4994-a643-7810d99c091d|b26099e0-6d81-45da-b6d5-6e90f01675d4|20241028173330",
        Code: "200",
        Msg: ["Success"],
        GSTEnabledAirline: "6E,S6E,IX,QP,...", // truncated for brevity
        TravellerCheckList: [
          {
            DOB: 0,
            PassportNo: 0,
            PLI: 0,
            Nationality: 0,
            VisaType: 0,
            Country: 0,
            PDOE: 0,
            DOI: 0,
            "PAN card": 0,
          },
        ],
      };

      // Update traveller check list state using a ref (assumed stable)
      setTravellCheckLists([
        ...travellerCheckListRef.current,
        data.TravellerCheckList[0],
      ]);

      if (i + 1 === getsPriceTuisRef.current.length) {
        const travel = adapter.setValidationPaxForm(
          travellerCheckListRef.current,
          travelFormRef.current
        );
        console.log("travel.form", travel.form);
        setTravelForm(travel.form);
        setTravellerCheckList(travel.checklist);
        setTravellerShimmer(false);
        callFlightSSRApi(0);
      } else {
        await callTravellCheckListApi(i + 1);
      }
    } catch (error) {
      console.log("error", error);
    }
  }, [
    adapter,
    travellerCheckListRef,
    travelFormRef,
    getsPriceTuisRef,
    setTravellCheckLists,
    setTravelForm,
    setTravellerCheckList,
    setTravellerShimmer,
    callFlightSSRApi,
  ]);


  // COMMENTED OUT: Using callSmartPricer data instead of calling separate Gets Price API
  // const callGetsPriceApi = useCallback(async (i: number): Promise<void> => {
  //   const body = { TUI: getsPriceTuisRef.current[i] };
  //   try {
  //     const data = await api.callGetsPrice(body);
  //     setGetsPriceData([]);
  //     const x = [...getsPriceDataRef.current];
  //     x[i] = data;
  //     setGetsPriceData(x);

  //     setFlightAddons((prev: any) => ({
  //       ...prev,
  //       trip: [...prev.trip, data],
  //     }));
  //     setFareSummaryArray([
  //       ...fareSummaryArrayRef.current,
  //       helper.setFareSummary(data),
  //     ]);
  //     if (i + 1 === getsPriceTuisRef.current.length) {
  //       if (fareSummaryArrayRef.current.length > 1) {
  //         setFareSummary(helper.addFareSummaries(fareSummaryArrayRef.current));
  //       } else {
  //         setFareSummary(fareSummaryArrayRef.current[0]);
  //       }
  //       setFareSummaryLoader(false);
  //       setSmartPriceLoader(false);
  //       setCardShimmer(false);
  //       setTravelForm(adapter.paxFormSet(data.ADT, data.CHD, data.INF));
  //       callTravellCheckListApi(0);
  //     } else {
  //       await callGetsPriceApi(i + 1);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }, [
  //   api,
  //   getsPriceTuisRef,
  //   getsPriceDataRef,
  //   setGetsPriceData,
  //   setFlightAddons,
  //   fareSummaryArrayRef,
  //   helper,
  //   setFareSummaryArray,
  //   setFareSummary,
  //   setFareSummaryLoader,
  //   setSmartPriceLoader,
  //   setCardShimmer,
  //   setTravelForm,
  //   adapter,
  //   callTravellCheckListApi,
  // ]);


  const callSmartPricer = useCallback(async (i: number): Promise<void> => {
    try {
      const data = await api.callSmartPricer(requestObjRef.current[i]);
      setGetsPriceData([...getsPriceDataRef.current, data]);
      const gspti = [...getsPriceTuisRef.current, data.TUI];
      setGetsPriceTuis(gspti);

      // Process data similar to what callGetsPriceApi was doing
      setFlightAddons((prev: any) => ({
        ...prev,
        trip: [...prev.trip, data],
      }));
      setFareSummaryArray([
        ...fareSummaryArrayRef.current,
        helper.setFareSummary(data),
      ]);

      if (i + 1 === requestObjRef.current.length) {
        sessionStorage.setItem('dygPriceListId', JSON.stringify(getsPriceTuisRef.current));

        // Set fare summary based on array length
        if (fareSummaryArrayRef.current.length > 1) {
          setFareSummary(helper.addFareSummaries(fareSummaryArrayRef.current));
        } else {
          setFareSummary(fareSummaryArrayRef.current[0]);
        }

        // Set loader states
        setFareSummaryLoader(false);
        setSmartPriceLoader(false);
        setCardShimmer(false);

        // Set travel form and call next API
        setTravelForm(adapter.paxFormSet(data.ADT, data.CHD, data.INF));
        callTravellCheckListApi(0);
      } else {
        // Use await to ensure proper promise chaining in recursion
        await callSmartPricer(i + 1);
      }
    } catch (error) {
      // handle error if needed
      console.log('callSmartPricer error:', error);
    }
  }, [
    api,
    requestObjRef,
    getsPriceDataRef,
    getsPriceTuisRef,
    setGetsPriceData,
    setGetsPriceTuis,
    setFlightAddons,
    fareSummaryArrayRef,
    helper,
    setFareSummaryArray,
    setFareSummary,
    setFareSummaryLoader,
    setSmartPriceLoader,
    setCardShimmer,
    setTravelForm,
    adapter,
    callTravellCheckListApi,
  ]);

  const callSmartPricerInTui = useCallback(async (i: number): Promise<void> => {
    const body = {
      Trips: [{ TUI: getsPriceTuis[i] }],
      Mode: "SY",
      Options: "",
      Source: "ST",
    };

    const data = await api.callSmartPricer(body);
    setGetsPriceData(data);
    setFlightAddons((prev: any) => ({
      ...prev,
      trip: [...prev.trip, data],
    }));
    const fs = [...fareSummaryArrayRef.current, data];
    setFareSummaryArray(fs);

    if (i + 1 === getsPriceTuis.length) {
      if (fareSummaryArray.length > 1) {
        setFareSummary(helper.addFareSummaries(fareSummaryArray));
      } else {
        setFareSummary(fareSummaryArray[0]);
      }
      setCardShimmer(false);
      setFareSummaryLoader(false);
      setSmartPriceLoader(false);
      console.log("adapter.paxFormSet(data.ADT, data.CHD, data.INF)", adapter.paxFormSet(data.ADT, data.CHD, data.INF));
      setTravelForm(adapter.paxFormSet(data.ADT, data.CHD, data.INF));
      callTravellCheckListApi(0);
    } else {
      // Recursive call using the same function reference from useCallback
      await callSmartPricerInTui(i + 1);
    }
  }, [
    getsPriceTuis, // array of TUI values
    api,           // your API service instance
    setGetsPriceData,
    setFlightAddons,
    fareSummaryArrayRef, // assuming this is a ref (stable)
    fareSummaryArray,    // state variable
    setFareSummaryArray,
    setFareSummary,
    setCardShimmer,
    setFareSummaryLoader,
    setSmartPriceLoader,
    helper,       // should be memoized (e.g., via useMemo)
    adapter,      // should be memoized (e.g., via useMemo)
    setTravelForm,
    callTravellCheckListApi,
  ]);


  const apiCall = useCallback(() => {
    if (sessionStorage && sessionStorage.getItem('dygPriceListId')) {
      setGetsPriceData(JSON.parse(sessionStorage.getItem('dygPriceListId') || '[]') as string[]);
      callSmartPricerInTui(0);
    } else {
      if (sessionStorage && sessionStorage.getItem('dyPricingBody')) {
        setRequestObj(JSON.parse(sessionStorage.getItem('dyPricingBody') || '[]') as any);
        callSmartPricer(0);
      } else {
        changeFlight();
      }
    }
  }, [setGetsPriceData, callSmartPricerInTui, setRequestObj, callSmartPricer, changeFlight]);


  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      setIsMobile(window.innerWidth < 768);


      // Subscribe to globalCurrency
      // Replace this with your state management or context API call
      // userService.globalCurrency.subscribe((data: string) => setCurrency(data));

      const searchSectorData = localStorage.getItem('dySearchSectorData');
      if (searchSectorData) {
        setSectType((JSON.parse(searchSectorData) as any)!.SecType);
      }

      const selectedFlight = sessionStorage.getItem('dySelectedFlight');
      if (selectedFlight) {
        setGetsPriceData([JSON.parse(selectedFlight)]);
        // setCardShimmer(false);
      }
      apiCall();
      window.addEventListener('resize', checkIsMobile);

      return () => {
        sessionStorage.removeItem('dygPriceListId');
        window.removeEventListener('resize', checkIsMobile);
      };
    }
  }, []);

  // async function navigateRegister() {
  //   await router.push('/')
  // }

  const checkIsMobile = () => {
    setIsMobile(window.innerWidth < 768);
  };



  const calculateAge = (dob: string): number => {
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const mapPaxTypeToPTC = (paxType: string): string => {
    switch (paxType) {
      case 'A': return 'adult';
      case 'C': return 'child';
      case 'I': return 'infant';
      default: return 'adult';
    }
  };

  const generateGender = (title: string): string => {
    const maleTitle = ['Mr', 'Master'];
    const femaleTitle = ['Ms', 'Mrs', 'Miss'];

    if (maleTitle.includes(title)) return 'M';
    if (femaleTitle.includes(title)) return 'F';
    return 'M'; // Default
  };

  const handleBookNow = async(travellerFormData:travel_form) => {
    try {
      // Check authentication first
      const token = api.getToken();

      if (!token) {
        alert('Please login first to make a booking');
        router.push('/'); // Redirect to login page
        return;
      }

      if (!travellerFormData) {
        alert('Please fill in all required fields');
        return;
      }

      // Validate required form fields
      if (!travellerFormData.email || !travellerFormData.phone) {
        alert('Please provide email and phone number');
        return;
      }

      if (!travellerFormData.travellers || travellerFormData.travellers.length === 0) {
        alert('Please add traveller information');
        return;
      }

      // Validate traveller data
      for (let i = 0; i < travellerFormData.travellers.length; i++) {
        const traveller = travellerFormData.travellers[i];
        if (!traveller || !traveller.Title || !traveller.FName || !traveller.LName) {
          alert(`Please fill in all required fields for ${traveller?.paxHead || `Traveller ${i + 1}`}`);
          return;
        }
      }

      const formData = travellerFormData;

      // Get flight data from state
      if (!getsPriceData || getsPriceData.length === 0) {
        alert('An Error Occured Please Try Refreshing The Page.');
        return;
      }

      const flightData = getsPriceData[0]; // Use first flight data

      // Validate flight data structure
      if (!flightData.TUI) {
        console.log('Flight data is missing TUI. Please select the flight again.',flightData);
        alert('Flight booking data is incomplete. Please select the flight again.');
        return;
      }

      // Build flight booking object
      const flightBooking = {
        provider_info: {
          code: String(flightData.Trips?.[0]?.Journey?.[0]?.Provider || "1G")
        },
        TUI: String(flightData.TUI || ""),
        ADT: Number(flightData.ADT || 1),
        CHD: Number(flightData.CHD || 0),
        INF: Number(flightData.INF || 0),
        NetAmount: Number(flightData.NetAmount || 0),
        Trips: flightData.Trips?.map((trip: any, tripIndex: number) => ({
          Journey: trip.Journey?.map((journey: any) => ({
            Provider: String(journey.Provider || "1G"),
            Stops: Number(journey.Stops || 0),
            Segments: journey.Segments?.map((segment: any, segmentIndex: number) => ({
              Flight: {
                FUID: String(segment.Flight.FUID || (segmentIndex + 1).toString()),
                VAC: String(segment.Flight.VAC || ""),
                MAC: String(segment.Flight.MAC || ""),
                OAC: String(segment.Flight.OAC || ""),
                Airline: String(segment.Flight.Airline || ""),
                FlightNo: String(segment.Flight.FlightNo || ""),
                ArrivalTime: String(segment.Flight.ArrivalTime || ""),
                DepartureTime: String(segment.Flight.DepartureTime || ""),
                ArrivalCode: String(segment.Flight.ArrivalCode || ""),
                DepartureCode: String(segment.Flight.DepartureCode || ""),
                Duration: String(segment.Flight.Duration || ""),
                FareBasisCode: String(segment.Flight.FareBasisCode || ""),
                ArrAirportName: String(segment.Flight.ArrAirportName || ""),
                DepAirportName: String(segment.Flight.DepAirportName || ""),
                RBD: String(segment.Flight.RBD || ""),
                Cabin: String(segment.Flight.Cabin || "ECONOMY"),
                Refundable: String(segment.Flight.Refundable || "N")
              },
              Fares: {
                GrossFare: Number(segment.Fares?.GrossFare || journey.GrossFare || 0),
                NetFare: Number(segment.Fares?.NetFare || journey.NetFare || 0)
              }
            })) || [],
            Offer: String(journey.Offer || "DefaultOffer"),
            OrderID: Number(journey.OrderID || tripIndex),
            GrossFare: Number(journey.GrossFare || 0),
            NetFare: Number(journey.NetFare || 0)
          })) || []
        })) || [],
        AirlineNetFare: Number(flightData.AirlineNetFare || flightData.NetAmount || 0),
        SSRAmount: Number(flightData.SSRAmount || 0),
        CrossSellAmount: Number(flightData.CrossSellAmount || 0),
        GrossAmount: Number(flightData.GrossAmount || flightData.NetAmount || 0),
        Hold: Boolean(flightData.Hold || false),
        ActualHoldTime: Number(flightData.ActualHoldTime || 0),
        ActualDisplayTime: Number(flightData.ActualDisplayTime || 0)
      };

      // Build travellers array
      const travellers = formData.travellers?.map((traveller: any, index: number) => ({
        ID: Number(index + 1),
        PaxID: Number(index + 1),
        Title: String(traveller.Title || "MR"),
        FName: String(traveller.FName || "John"),
        LName: String(traveller.LName || "Doe"),
        Age: Number(traveller.DOB ? calculateAge(traveller.DOB) : 25), // Default age if DOB not provided
        DOB: String(traveller.DOB || "1989-05-15"),
        Gender: String(generateGender(traveller.Title)),
        PTC: String(mapPaxTypeToPTC(traveller.paxType)),
        PLI: String(traveller.PLI || "New York"),
        PDOE: String(traveller.PDOE || "2030-12-31"),
        Nationality: String(traveller.Nationality || "US"),
        PassportNo: String(traveller.PassportNo || "A123456789"),
        VisaType: traveller.VisaType || null,
        DocType: String(traveller.PassportNo ? "Passport" : "Other")
      })) || [];

      // Build contact info from first traveller and form data
      const firstTraveller = formData.travellers?.[0];
      const contactInfo = {
        Title: String((firstTraveller as any)?.Title || "MR"),
        FName: String((firstTraveller as any)?.FName || "John"),
        LName: String((firstTraveller as any)?.LName || "Hnoo"),
        Mobile: String(formData.phone || "*********"),
        Phone: null,
        Email: String(formData.email || "<EMAIL>"),
        Address: String("123 Elm street, New York , NY ,USA"), // Not available in form
        CountryCode: String(formData.phone_code || "+91"),
        MobileCountryCode: String(formData.phone_code || "+91"),
        State: String("NY"), // Not available in form
        City: String("New York"), // Not available in form
        PIN: null,
        GSTAddress: null,
        GSTCompanyName: null,
        GSTTIN: null,
        UpdateProfile: Boolean(false),
        IsGuest: Boolean(true),
        SaveGST: Boolean(false),
        Language: null
      };

      const body: BookNowBody = {
        flight_booking: flightBooking as any,
        Travellers: travellers as any,
        ContactInfo: contactInfo as any
      };

      setIsLoad(true); // Show loading state

      const data = await api.bookNowApi(body);

      console.log('API Response:', data); // Temporary log to see the actual response format

      // Check for various success response formats
      // First check for standard success indicators
      if(data.code === 200 || data.Code === 200 || data.status === 200 || data.Status === 200 ||
         data.success === true || data.Success === true || data.code === "200" || data.Code === "200") {
        router.push('/flight/FlightSuccess');
      }
      // Check for booking response format (MasterBooking and FlightBooking objects with IDs)
      else if(data.MasterBooking && data.FlightBooking &&
              data.MasterBooking.id && data.FlightBooking.id) {
        // This indicates successful booking creation
        console.log('Booking created successfully:', {
          masterBookingId: data.MasterBooking.id,
          flightBookingId: data.FlightBooking.id,
          bookingReference: data.MasterBooking.booking_reference
        });

        // Store booking data for success page
        localStorage.setItem('dyLatestBooking', JSON.stringify(data));

        router.push('/flight/FlightSuccess');
      } else {
        alert(`Booking failed: ${data.message || data.Msg || data.error || 'Unknown error'}`);
      }
    } catch(error) {
      if (error instanceof Error) {
        alert(`Booking failed: ${error.message}`);
      } else {
        alert('Booking failed due to an unexpected error. Please try again.');
      }
    } finally {
      setIsLoad(false); // Hide loading state
    }
  }



  // const getAddonsData = (event: any[]) => {
  //   const updatedFareSummary = { ...fareSummary };
  //   updatedFareSummary.totalAmount = fareSummary.totalWithotAddons;
  //   updatedFareSummary.addons.total = 0;
  //   updatedFareSummary.addons.subFare = [];

  //   event.forEach((x) => {
  //     if (x.selectedCount > 0) {
  //       updatedFareSummary.addons.subFare.push({ title: x.title, totalPrice: x.totalPrice });
  //       updatedFareSummary.addons.total += x.totalPrice;
  //       updatedFareSummary.totalAmount += x.totalPrice;
  //     }
  //   });

  //   setFareSummary(updatedFareSummary);
  // };




  // function hidePayment() {

  // }

  // Removed automatic booking call - now only triggered by user action

  function makePayment(data:travel_form) {
    handleBookNow(data)
  }

  function showFareSummary() {

  }

  function hideFareSummary() {

  }

  return (
    <div className={styles["flight-review-div"]}>
      <div className={styles["head-ribben-div"]}>
        <div className="container">
          <h1 className={styles["head_font"]}>Review your Flight</h1>
        </div>
      </div>
      {cardShimmer && (
                  <Loader />
      )}
      <div className="container">
        <div className={styles["body-div"]}>
          <div className={styles["flight-details-div"]}>
            <section>
              <div className={styles["header-div"]}>
                <div className="linkBtn" onClick={changeFlight}>
                  Change Flight
                </div>
              </div>
              {getsPriceData.map((tripData, index) => (
                  <AirlineItineryCard
                    key={index}
                    initialTripData={tripData}
                    currency={currency}
                    isLoading={cardShimmer}
                    smartPriceLoader={smartPriceLoader} isItinerary={false} />
                ))}
            </section>

            <section>
             <FlightReviewTravellerForm
                  handleMakePayment={makePayment}
                  travellerForm={travelForm}
                  travellerCheckList={travellerCheckList}
                  pax={[1,2]}
                />
              {/* {travellerShimmer ? (
                <ShimmerComponent type="travelForm" />
              ) : (
                <FlightReviewTravellerForm
                  travellerForm={travelForm}
                  travellerCheckList={travellerCheckList}
                />
              )} */}
            </section>



            {/* Addons Section */}
            {/* <section>
              {addonsShimmer ? (
                <ShimmerComponent type="addons" />
              ) : (
                !emptyArray && (
                  <FlightAddonSection
                    ssrData={ssrData}
                    emitAddOns={getAddonsData}
                    currency={currency}
                    isMobile={isMobile} />
                )
              )}
            </section> */}
{/*
            <section>
              <div className={styles["payment-bttn-div"]}>
                <button className="dy_primary_bttn" onClick={makePayment}>
                  Make Payment
                </button>
              </div>
            </section> */}
          </div>

          <div className={styles["price-div"]}>
            <div className={styles["fare-details-data-div"]}>
              <h2>Fare Details</h2>
              <div className={styles["fare-summary-div"]}>
                {fareSummaryLoader ? (
                  <ShimmerComponent type="faresummary" />
                ) : (
                  <FlightReviewFareSummary
                    fareSummary={fareSummary}
                    currency={currency}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles["mobile-submit-div"]}>
          <div className={styles["fare-div"]}>
            <span className={styles["fr-txt"]}>Total</span>
            <div className={styles["fr-amt"]}>
              <span>{fareSummary?.totalAmount} {currency}</span>
              <InfoIcon className={styles["icon"]} onClick={showFareSummary}/>
            </div>
          </div>
          <button
            className="dy_primary_bttn"
            onClick={() => {
              // Trigger form submission by clicking the form submit button
              const formSubmitButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
              if (formSubmitButton) {
                formSubmitButton.click();
              } else {
                alert('Please fill in all required fields first');
              }
            }}
          >
            Make Payment
          </button>
        </div>

      {isMobile && (
        <DyBottomUpPopup show={showPriceSummary} hide={hideFareSummary} heading="Fare Details" >
          <FlightReviewFareSummary fareSummary={fareSummary} currency={currency} />
        </DyBottomUpPopup>
      )}
      {isLoad && (
            <Loader />
      )}
    </div>
  );
};

export default FlightReview;