import moment from 'moment';

export interface TripPaxArray {
  totalFare: number;
  paxArray: PaxArray[];
}

export interface PaxArray {
  id: number;
  paxId: number;
  ptc: string;
  pax: string;
  name: string;
  sector: string;
  ticket: string;
  status: string;
  basicFare: number;
  taxCharge: number;
  totalFare: number;
}

const ItineraryFlightCardHelper = () => {
  
  const setFlightCardSegments = (trips: any[], ssr: any[], rules: any[]) => {
    const segmentArray: any[] = [];

    trips.forEach((trip: any) => {
      const journey = trip.Journey[0];
      journey.expand = false;
      journey.Stops = 0;
      journey.segmentArray = [];
      journey.from = journey.Segments[0]?.Flight?.DepAirportName || '';
      journey.to = journey.Segments[journey.Segments.length - 1]?.Flight?.ArrAirportName || '';
      journey.DepartureTime = journey.Segments[0]?.Flight?.DepartureTime || '';
      journey.Refundable = journey.Segments[0]?.Flight?.Refundable || '';
      journey.AirlinePNR = journey.Segments[0]?.Flight?.APNR || '';
      journey.CRSPNR = journey.Segments[0]?.Flight?.CRSPNR || '';
      journey.Status = journey.Status ? GetFLTTransHistoryStatus(journey.Status, "") : '';
      journey.fareRule = setFareRule(rules, journey?.Segments[0]?.Flight?.FUID.toString());

      journey.Segments.forEach((seg: any, segIndex: number) => {
        const segValue = { ...seg.Flight };
        segValue.isHops = false;
        segValue.isConnection = false;
        segValue.connectionTime = '';
        segValue.connectionAirport = '';
        segValue.connectionAirportCode = '';
        if (segIndex > 0) {
          journey.Stops++;
        } else {
          journey.MAC = seg.Flight.MAC;
        }

        segValue.baggage = setBaggage(segValue.FUID.toString(), ssr);
        segValue.MAC = getMainProvider(seg.Flight.MAC);
        segValue.OAC = getMainProvider(seg.Flight.OAC);
        segValue.VAC = getMainProvider(seg.Flight.VAC);
        segValue.isNextDay = isNextDay(segValue.DepartureTime, segValue.ArrivalTime);
        segValue.travelClass = getTravelClass(segValue.Cabin);
        segValue.Duration = setTimeHvrMnt(segValue.Duration);

        if (segValue.Hops && segValue.Hops.length > 0) {
          segValue.Hops.forEach((hops: any) => {
            journey.Stops++;

            const hopSegValue1 = { ...segValue };
            hopSegValue1.Duration = setTimeHvrMnt(hops.ArrivalDuration);
            hopSegValue1.ArrAirportName = hops.ArrAirportName;
            hopSegValue1.ArrivalCode = hops.ArrivalCode;
            hopSegValue1.ArrivalTime = hops.ArrivalTime;

            if (segIndex > 0) {
              hopSegValue1.isConnection = true;
              hopSegValue1.connectionTime = setLayoutTime(seg.Flight.DepartureTime, trip.Journey[0].Segments[segIndex - 1].Flight.ArrivalTime);
              hopSegValue1.connectionAirport = seg.Flight.DepAirportName;
              hopSegValue1.connectionAirportCode = seg.Flight.DepartureCode;
            }

            hopSegValue1.isNextDay = isNextDay(hopSegValue1.DepartureTime, hopSegValue1.ArrivalTime);
            journey.segmentArray.push(hopSegValue1);
            segmentArray.push(hopSegValue1);

            const hopSegValue2 = { ...segValue };
            hopSegValue2.isHops = true;
            hopSegValue2.connectionTime = setTimeHvrMnt(hops.Duration);
            hopSegValue2.connectionAirport = hops.ArrAirportName;
            hopSegValue2.connectionAirportCode = hops.ArrivalCode;
            hopSegValue2.DepAirportName = hops.ArrAirportName;
            hopSegValue2.DepartureCode = hops.ArrivalCode;
            hopSegValue2.DepartureTime = hops.DepartureTime;
            hopSegValue2.Duration = setTimeHvrMnt(hops.DepartureDuration);
            hopSegValue2.isNextDay = isNextDay(hopSegValue2.DepartureTime, hopSegValue2.ArrivalTime);

            journey.segmentArray.push(hopSegValue2);
            segmentArray.push(hopSegValue2);
          });
        } else {
          if (segIndex > 0) {
            segValue.isConnection = true;
            segValue.connectionTime = setLayoutTime(seg.Flight.DepartureTime, trip.Journey[0].Segments[segIndex - 1].Flight.ArrivalTime);
            segValue.connectionAirport = seg.Flight.DepAirportName;
            segValue.connectionAirportCode = seg.Flight.DepartureCode;
          }

          journey.segmentArray.push(segValue);
          segmentArray.push(segValue);
        }
      });
    });

    return trips;
  };

  const setFareRule = (rules: any[], fuid: string) => {
    return rules.find((x: any) => x.FUID.split(',').includes(fuid)) || null;
  };

  const setTimeHvrMnt = (time: string): string => {
    return time.slice(0, 2) + ' Hr: ' + time.slice(4, 6) + ' Min';
  };

  function GetFLTTransHistoryStatus(strCurStatus: string, strCancelReqstStatus: string,isFromApprovalOrRequest?:boolean): string {

    try {
        if(!isFromApprovalOrRequest){
        if (!strCancelReqstStatus) {
            strCancelReqstStatus = strCancelReqstStatus.trim();
        }

        if (strCurStatus.includes(",")) {
            //Failed
            if (strCurStatus.includes("BO1") || strCurStatus.includes("BR1"))
                return "Failed";

            //In Progress
            else if (strCurStatus.includes("TO1") || strCurStatus.includes("TR1") || strCurStatus.includes("BO0") || strCurStatus.includes("BR0")) {
                return "In Progress";
            }

            //Confirmed
            else if (strCurStatus.split(',')[0] == "TO0" && strCurStatus.split(',')[1] == "TR0")
                return "Confirmed";

            //On Hold
            else if (strCurStatus.includes("HO0") || strCurStatus.includes("HR0"))
                return "On Hold";
            else if (strCurStatus.includes("HO1") || strCurStatus.includes("HR1"))
                return "Hold Failed";


            // QPNR On Hold

            else if (strCurStatus.includes("QO0") || strCurStatus.includes("QR0"))
                return "Q PNR HOLD";

            // QPNR Released

            else if (strCurStatus.includes("QC0"))
                return "Q PNR Released";

            // QPNR Release Failed

            else if (strCurStatus.includes("ORR"))
                return "Reissue-Req   ";
            else if (strCurStatus.includes("ORJ"))
                return "Reissue-Rej   ";
            else if (strCurStatus.includes("QC1"))
                return "Q PNR Release Failed";

            else if (strCurStatus.includes("HC0"))
                return "PNR RELESED";

            else if (strCurStatus.includes("HC1"))
                return "PNR RELEASE FAILED";

            //Hold Rejected
            else if (strCurStatus.includes("RO0") || strCurStatus.includes("RR0"))
                return "Hold Rejected";
            
            else if (strCurStatus.includes('U1')){
                return 'Payment Failed';
            }    

            //In Progress
            else {
                if(isFromApprovalOrRequest){
                    return strCurStatus
                }
                return "In Progress";
            }
        }

        else {
            //Confirmed
            if (strCurStatus == "TO0" || strCurStatus == "TR0" || strCurStatus == "OT" || strCurStatus == "CR" || strCurStatus == "CJ" || strCurStatus == "CA")
                return "Confirmed";

            //In Progress
            else if (strCurStatus == "TO1" || strCurStatus == "TR1" || strCurStatus == "BO0" || strCurStatus == "BR0") {
                return "In Progress";
            }

            //Failed
            else if (strCurStatus == "BO1" || strCurStatus == "BR1")
                return "Failed";

            //On Hold
            else if (strCurStatus.includes("HO0") || strCurStatus.includes("HR0"))
                return "On Hold";
            else if (strCurStatus.includes("HO1") || strCurStatus.includes("HR1"))
                return "Hold Failed";

            // QPNR On Hold

            else if (strCurStatus.includes("QO0") || strCurStatus.includes("QR0"))
                return "Q PNR HOLD";

            // QPNR Released

            else if (strCurStatus.includes("QC0"))
                return "Q PNR Released";

            // QPNR Release Failed

            else if (strCurStatus.includes("QC1"))
                return "Q PNR Release Failed";

            else if (strCurStatus.includes("HC0"))
                return "PNR RELESED";

            else if (strCurStatus.includes("HC1"))
                return "PNR RELEASE FAILED";

            //Hold Rejected
            else if (strCurStatus.includes("RO0") || strCurStatus.includes("RR0"))
                return "Hold Rejected";

            //Partially Cancelled
            else if (strCurStatus == "CP") {
                if (strCancelReqstStatus == "Void") {
                    return "Void Part Cancelled";
                }
                else if (strCancelReqstStatus == "Refund") {
                    return "Refund Part Cancelled";
                }
                else if (strCancelReqstStatus == "Reschedule") {
                    return "Partially Rescheduled";
                }
                else if (strCancelReqstStatus != null && strCancelReqstStatus.includes(",")) {
                    if (strCancelReqstStatus.split(',')[0] == "Void" && strCancelReqstStatus.split(',')[1] == "Void" && strCancelReqstStatus.split(',')[2] == "Void")
                        return "Void Part Cancelled";
                    if (strCancelReqstStatus.split(',')[0] == "Reschedule" && strCancelReqstStatus.split(',')[1] == "Reschedule" && strCancelReqstStatus.split(',')[2] == "Reschedule")
                        return "Partially Rescheduled";
                    if (strCancelReqstStatus.split(',')[0] == "Refund" && strCancelReqstStatus.split(',')[1] == "Refund" && strCancelReqstStatus.split(',')[2] == "Refund")
                        return "Refund Part Cancelled";
                    else
                        return "Part Cancelled";
                }
                else if (strCancelReqstStatus == "" || strCancelReqstStatus == null) {
                    return "Part Cancelled";
                }
            }
            //Cancelled
            else if (strCurStatus == "CD" || strCurStatus == "CV" || strCurStatus == "CT")
                return "Cancelled";
            //Agent Confirmation Pending 
            else if (strCurStatus == "AP")
                return "Agent Confirmation Pending ";

            //Airline Confirmation Pending  
            else if (strCurStatus == "FP")
                return "Airline Confirmation Pending ";
            else if (strCurStatus == "ORR")
                return "Reissue-Req";
            else if (strCurStatus == "ORJ")
                return "Reissue-Rej";
            //Refunded
            else if (strCurStatus == "CF")
                return "Refunded";

            //Offline Requested
            else if (strCurStatus == "OR")
                return "Offline Requested";

            //Offline Rejected
            else if (strCurStatus == "OJ")
                return "Offline Rejected";

            //Offline Approved
            else if (strCurStatus == "OA")
                return "Offline Approved";
            //CANCELLED(R)
            else if (strCurStatus == "CDR")
                return "Cancelled (R)";
            //CANCELLED(R)
            else if (strCurStatus == "CPR")
                return "Part Canceld-R";
            // PAYMENT FAILED
            else if(strCurStatus == 'U1'){
                return 'Payment Failed';
            }    

            //In Progress
            else {
                return "In Progress";
            }
        }
    }else{
        return strCurStatus;
    }

    }

    catch (error) {
    }
    if(isFromApprovalOrRequest){
        return strCurStatus
    }else{
    return "In Progress";
    }

}

  const getTravelClass = (code: string): string => {
    if (code) {
      switch (code.toUpperCase()) {
        case 'E': return 'Economy';
        case 'PE': return 'Premium Economy';
        case 'F': return 'First Class';
        case 'B': return 'Business';
        default: return code.toLowerCase();
      }
    }
    return '';
  };

  const setBaggage = (fuid: string, ssr: any[]) => {
    const bagSSR = { checkin: Array<any>(), cabin: Array<any>() };
    const passengerTypes = { ADT: 'Adult', CHD: 'Child', INF: 'Infant' };

    const processedPassengerTypes: any = {};

    ssr.forEach((ssrItem: any) => {
      if (ssrItem.Code === 'BAG' && ssrItem.FUID.split(',').includes(fuid)) {
        const ptc = ssrItem.PTC;
        if (!processedPassengerTypes[ptc]) {
          const [checkinBaggage, cabinBaggage] = ssrItem.Description.split(',');
        //   bagSSR.checkin.push(`${passengerTypes[ptc]}-${checkinBaggage === '0Kg' ? 'No Baggage' : checkinBaggage}`);
        //   bagSSR.cabin.push(`${passengerTypes[ptc]}-${cabinBaggage === '0Kg' ? 'No Baggage' : cabinBaggage}`);
          processedPassengerTypes[ptc] = true;
        }
      }
    });

    return bagSSR;
  };

  const getMainProvider = (mac: string): string => {
    if (mac && mac.slice(-2) === '6E') {
        return '6E'
    } else if (mac && mac.slice(-2) === 'SG') {
        return 'SG'
    } else if (mac && mac.slice(-2) === 'G8') {
        return 'G8'
    } else if (mac && mac.slice(-2) === 'AK') {
        return 'AK'
    } else if (mac && mac.slice(-2) === 'I5') {
        return 'I5'
    } else {
        return mac
    }
  };

  const setLayoutTime = (depTime: any, arrTime: any): string => {
    const dt1 = moment(arrTime);
    const dt2 = moment(depTime);

    const duration = moment.duration(dt2.diff(dt1));
    return duration.hours().toString().padStart(2, '0') + ' Hr: ' + duration.minutes().toString().padStart(2, '0') + ' Min';
  };

  const isNextDay = (departureTime: any, arrivalTime: any): boolean => {
    const startDate = moment(departureTime).format('YYYY-MM-DD');
    const endDate = moment(arrivalTime).format('YYYY-MM-DD');
    return moment(endDate).diff(moment(startDate), 'days') > 0;
  };

  return {
    setFlightCardSegments,
    setFareRule,
    setTimeHvrMnt,
    getTravelClass,
    setBaggage,
    getMainProvider,
    setLayoutTime,
    isNextDay,
  };
};

export default ItineraryFlightCardHelper;
